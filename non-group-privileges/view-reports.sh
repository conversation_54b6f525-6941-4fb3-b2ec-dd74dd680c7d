#!/bin/bash

# View IAM Group-Based Access Control Reports
# This script helps view and analyze the various reports generated during remediation

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

show_help() {
    echo "Usage: $0 [OPTION]"
    echo
    echo "View IAM Group-Based Access Control reports"
    echo
    echo "Options:"
    echo "  --analysis     Show latest analysis report"
    echo "  --remediation  Show latest remediation report"
    echo "  --validation   Show latest validation report"
    echo "  --summary      Show summary of all reports"
    echo "  --list         List all available reports"
    echo "  --help         Show this help message"
    echo
}

list_reports() {
    echo -e "${BLUE}=== Available Reports ===${NC}"
    echo
    
    for report_type in analysis remediation validation; do
        local report_dir="${SCRIPT_DIR}/iam-${report_type}-reports"
        if [[ -d "$report_dir" ]]; then
            echo -e "${YELLOW}${report_type^} Reports:${NC}"
            ls -la "$report_dir" | grep -E "\.(txt|csv|json)$" | awk '{print "  " $9 " (" $5 " bytes, " $6 " " $7 " " $8 ")"}'
            echo
        else
            echo -e "${RED}No ${report_type} reports found${NC}"
            echo
        fi
    done
}

show_latest_report() {
    local report_type="$1"
    local report_dir="${SCRIPT_DIR}/iam-${report_type}-reports"
    
    if [[ ! -d "$report_dir" ]]; then
        echo -e "${RED}No ${report_type} reports directory found${NC}"
        return 1
    fi
    
    local latest_report=$(ls -t "${report_dir}"/iam-${report_type}-*.txt 2>/dev/null | head -1)
    
    if [[ -z "$latest_report" ]]; then
        echo -e "${RED}No ${report_type} reports found${NC}"
        return 1
    fi
    
    echo -e "${BLUE}=== Latest ${report_type^} Report ===${NC}"
    echo "File: $(basename "$latest_report")"
    echo
    cat "$latest_report"
}

show_summary() {
    echo -e "${BLUE}=== IAM Group-Based Access Control Summary ===${NC}"
    echo
    
    # Show CSV data
    local csv_file="${SCRIPT_DIR}/Failing-Resources-For-Test-217-AWS-IAM-Group-Based-Access-Control-********.csv"
    if [[ -f "$csv_file" ]]; then
        local total_users=$(tail -n +2 "$csv_file" | wc -l)
        local unique_accounts=$(tail -n +2 "$csv_file" | cut -d',' -f5 | tr -d '"' | sort -u | wc -l)
        
        echo -e "${YELLOW}Original DRATA Findings:${NC}"
        echo "  Total Users: $total_users"
        echo "  Affected Accounts: $unique_accounts"
        echo
    fi
    
    # Show latest analysis summary if available
    local analysis_dir="${SCRIPT_DIR}/iam-analysis-reports"
    if [[ -d "$analysis_dir" ]]; then
        local latest_analysis_summary=$(ls -t "${analysis_dir}"/iam-user-summary-*.csv 2>/dev/null | head -1)
        if [[ -n "$latest_analysis_summary" ]]; then
            echo -e "${YELLOW}Latest Analysis Summary:${NC}"
            echo "File: $(basename "$latest_analysis_summary")"
            
            if [[ -f "$latest_analysis_summary" ]]; then
                local analyzed_users=$(tail -n +2 "$latest_analysis_summary" | wc -l)
                local successful_analysis=$(tail -n +2 "$latest_analysis_summary" | grep -c "SUCCESS" || echo "0")
                
                echo "  Users Analyzed: $analyzed_users"
                echo "  Successful Analysis: $successful_analysis"
                
                # Show user type breakdown
                echo
                echo "  User Type Breakdown:"
                tail -n +2 "$latest_analysis_summary" | cut -d',' -f4 | sort | uniq -c | while read count type; do
                    echo "    $type: $count"
                done
            fi
            echo
        fi
    fi
    
    # Show latest validation summary if available
    local validation_dir="${SCRIPT_DIR}/iam-validation-reports"
    if [[ -d "$validation_dir" ]]; then
        local latest_validation_summary=$(ls -t "${validation_dir}"/iam-validation-summary-*.csv 2>/dev/null | head -1)
        if [[ -n "$latest_validation_summary" ]]; then
            echo -e "${YELLOW}Latest Validation Summary:${NC}"
            echo "File: $(basename "$latest_validation_summary")"
            
            if [[ -f "$latest_validation_summary" ]]; then
                local validated_users=$(tail -n +2 "$latest_validation_summary" | wc -l)
                local compliant_users=$(tail -n +2 "$latest_validation_summary" | grep -c "COMPLIANT" || echo "0")
                local non_compliant_users=$(tail -n +2 "$latest_validation_summary" | grep -c "NON_COMPLIANT" || echo "0")
                
                echo "  Users Validated: $validated_users"
                echo "  Compliant Users: $compliant_users"
                echo "  Non-Compliant Users: $non_compliant_users"
                
                if [[ $validated_users -gt 0 ]]; then
                    local compliance_rate=$(( compliant_users * 100 / validated_users ))
                    echo "  Compliance Rate: ${compliance_rate}%"
                fi
            fi
            echo
        fi
    fi
    
    # Show latest remediation summary if available
    local remediation_dir="${SCRIPT_DIR}/iam-remediation-reports"
    if [[ -d "$remediation_dir" ]]; then
        local latest_remediation_summary=$(ls -t "${remediation_dir}"/iam-remediation-summary-*.csv 2>/dev/null | head -1)
        if [[ -n "$latest_remediation_summary" ]]; then
            echo -e "${YELLOW}Latest Remediation Summary:${NC}"
            echo "File: $(basename "$latest_remediation_summary")"
            
            if [[ -f "$latest_remediation_summary" ]]; then
                local remediated_users=$(tail -n +2 "$latest_remediation_summary" | wc -l)
                local successful_remediations=$(tail -n +2 "$latest_remediation_summary" | grep -c "SUCCESS\|DRY_RUN" || echo "0")
                
                echo "  Users Processed: $remediated_users"
                echo "  Successful Actions: $successful_remediations"
            fi
            echo
        fi
    fi
}

# Parse command line arguments
case "${1:-}" in
    --analysis)
        show_latest_report "analysis"
        ;;
    --remediation)
        show_latest_report "remediation"
        ;;
    --validation)
        show_latest_report "validation"
        ;;
    --summary)
        show_summary
        ;;
    --list)
        list_reports
        ;;
    --help)
        show_help
        ;;
    "")
        show_summary
        ;;
    *)
        echo "Unknown option: $1"
        echo "Use --help for usage information"
        exit 1
        ;;
esac
