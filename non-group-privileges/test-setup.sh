#!/bin/bash

# Test setup for IAM Group-Based Access Control remediation
# This script validates prerequisites and runs basic tests

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== IAM Group-Based Access Control Setup Test ===${NC}"
echo

# Check prerequisites
echo -e "${YELLOW}Checking prerequisites...${NC}"

# Check AWS CLI
if command -v aws >/dev/null 2>&1; then
    echo -e "${GREEN}✓ AWS CLI installed${NC}"
    aws --version
else
    echo -e "${RED}✗ AWS CLI not found${NC}"
    exit 1
fi

# Check jq
if command -v jq >/dev/null 2>&1; then
    echo -e "${GREEN}✓ jq installed${NC}"
    jq --version
else
    echo -e "${RED}✗ jq not found - please install jq${NC}"
    exit 1
fi

# Check bash version
if [[ ${BASH_VERSION%%.*} -ge 4 ]]; then
    echo -e "${GREEN}✓ Bash 4+ available${NC}"
    echo "Bash version: $BASH_VERSION"
else
    echo -e "${RED}✗ Bash 4+ required${NC}"
    exit 1
fi

echo

# Check AWS credentials
echo -e "${YELLOW}Checking AWS credentials...${NC}"
if aws sts get-caller-identity >/dev/null 2>&1; then
    echo -e "${GREEN}✓ AWS credentials configured${NC}"
    aws sts get-caller-identity
else
    echo -e "${RED}✗ AWS credentials not configured${NC}"
    exit 1
fi

echo

# Check CSV file
echo -e "${YELLOW}Checking data files...${NC}"
CSV_FILE="${SCRIPT_DIR}/Failing-Resources-For-Test-217-AWS-IAM-Group-Based-Access-Control-********.csv"
if [[ -f "$CSV_FILE" ]]; then
    echo -e "${GREEN}✓ DRATA findings CSV found${NC}"
    local total_lines=$(wc -l < "$CSV_FILE")
    local data_lines=$((total_lines - 1))
    echo "  Total users to remediate: $data_lines"
    
    # Show sample of accounts
    echo "  Sample accounts:"
    tail -n +2 "$CSV_FILE" | cut -d',' -f5,6 | tr -d '"' | sort -u | head -5 | while IFS=',' read account_id account_name; do
        echo "    $account_id - $account_name"
    done
else
    echo -e "${RED}✗ DRATA findings CSV not found${NC}"
    echo "Expected: $CSV_FILE"
    exit 1
fi

echo

# Test cross-account role access on a sample account
echo -e "${YELLOW}Testing cross-account role access...${NC}"
SAMPLE_ACCOUNT=$(tail -n +2 "$CSV_FILE" | head -1 | cut -d',' -f5 | tr -d '"')
ROLE_ARN="arn:aws:iam::${SAMPLE_ACCOUNT}:role/CrossAccountAuditRole"

echo "Testing role: $ROLE_ARN"

if aws sts assume-role --role-arn "$ROLE_ARN" --role-session-name "SetupTest-$(date +%s)" >/dev/null 2>&1; then
    echo -e "${GREEN}✓ Cross-account role access working${NC}"
else
    echo -e "${RED}✗ Cannot assume cross-account role${NC}"
    echo "Please ensure cross-account roles are deployed"
    echo "See: ../cross-account-roles/ for setup instructions"
    exit 1
fi

echo

# Test script permissions
echo -e "${YELLOW}Checking script permissions...${NC}"
for script in analyze-iam-users.sh remediate-group-access.sh validate-group-access.sh view-reports.sh; do
    if [[ -x "${SCRIPT_DIR}/${script}" ]]; then
        echo -e "${GREEN}✓ ${script} is executable${NC}"
    else
        echo -e "${RED}✗ ${script} is not executable${NC}"
        chmod +x "${SCRIPT_DIR}/${script}"
        echo -e "${YELLOW}  Fixed permissions for ${script}${NC}"
    fi
done

echo

# Create report directories
echo -e "${YELLOW}Creating report directories...${NC}"
for dir in iam-analysis-reports iam-remediation-reports iam-validation-reports; do
    mkdir -p "${SCRIPT_DIR}/${dir}"
    echo -e "${GREEN}✓ Created ${dir}${NC}"
done

echo

# Run a quick analysis test on one user
echo -e "${YELLOW}Running quick analysis test...${NC}"
SAMPLE_USER=$(tail -n +2 "$CSV_FILE" | head -1 | cut -d',' -f7 | tr -d '"')
echo "Testing analysis on user: $SAMPLE_USER in account: $SAMPLE_ACCOUNT"

# Create a minimal test version of the analysis
TEMP_CSV=$(mktemp)
head -1 "$CSV_FILE" > "$TEMP_CSV"
tail -n +2 "$CSV_FILE" | head -1 >> "$TEMP_CSV"

echo "Running analysis on single user..."
if timeout 30 "${SCRIPT_DIR}/analyze-iam-users.sh" 2>/dev/null; then
    echo -e "${GREEN}✓ Analysis script working${NC}"
else
    echo -e "${YELLOW}⚠ Analysis test timed out or failed - this is normal for the test${NC}"
fi

rm -f "$TEMP_CSV"

echo

# Summary
echo -e "${GREEN}=== Setup Test Complete ===${NC}"
echo
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Run full analysis: ./analyze-iam-users.sh"
echo "2. Review analysis reports: ./view-reports.sh --analysis"
echo "3. Test remediation (dry run): ./remediate-group-access.sh"
echo "4. Execute remediation: ./remediate-group-access.sh --execute"
echo "5. Validate compliance: ./validate-group-access.sh"
echo
echo -e "${BLUE}All prerequisites met! Ready for IAM group-based access remediation.${NC}"
