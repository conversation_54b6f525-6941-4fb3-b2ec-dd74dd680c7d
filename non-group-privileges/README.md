# AWS IAM Group-Based Access Control Remediation

This directory contains scripts and tools for remediating DRATA finding **Test-217: AWS IAM Group-Based Access Control**.

## Overview

DRATA has identified IAM users with inline policies or direct policy attachments across your AWS organization. This violates the security best practice of using group-based access control, which provides:

- **Unified permissions management** through a single, flexible layer
- **Reduced likelihood of excessive permissions**
- **Easier auditing and compliance**
- **Simplified user lifecycle management**

## Files in this Directory

### Data Files
- `Failing-Resources-For-Test-217-AWS-IAM-Group-Based-Access-Control-05302025.csv` - DRATA findings export

### Scripts
- `analyze-iam-users.sh` - Analyzes current IAM user configurations
- `remediate-group-access.sh` - Migrates users to group-based access
- `validate-group-access.sh` - Validates compliance after remediation

### Report Directories
- `iam-analysis-reports/` - Analysis results and user categorization
- `iam-remediation-reports/` - Remediation actions and backups
- `iam-validation-reports/` - Compliance validation results

## Current Findings Summary

Based on the DRATA export, we have **52 IAM users** across **multiple AWS accounts** with non-compliant configurations:

### Affected Accounts
- **************** (aws-account-ml) - 4 users
- **************** (aws-account-vpc-questdiag) - 3 users  
- **************** (Incode Technologies T2 9688) - 13 users
- **************** (AWS Account Canada SAAS) - 5 users
- **************** (Microformas_Citi) - 4 users
- And 15+ additional accounts

### User Categories Identified
- **GitHub Service Accounts** - CI/CD automation users
- **Keycloak Service Accounts** - Identity management service users
- **Terraform Service Accounts** - Infrastructure automation users
- **ML/ClearML Service Accounts** - Machine learning platform users
- **Vonage/TokBox Integration** - Video service integration users
- **Human Users** - Developer and administrator accounts
- **DRP Service Accounts** - Disaster recovery automation users

## Remediation Strategy

### Phase 1: Analysis
1. **Run analysis script** to understand current configurations
2. **Categorize users** by type and function
3. **Identify common permission patterns** for group creation
4. **Plan group structure** based on user types and roles

### Phase 2: Group Creation and Testing
1. **Create IAM groups** for each user category
2. **Test group permissions** in development accounts first
3. **Validate functionality** before production migration
4. **Document group policies** and membership criteria

### Phase 3: Migration
1. **Backup current configurations** before changes
2. **Migrate policies** from users to appropriate groups
3. **Add users to groups** and remove direct permissions
4. **Validate compliance** with DRATA requirements

## Usage Instructions

### Prerequisites
- AWS CLI configured with appropriate credentials
- Cross-account IAM roles deployed (see `../cross-account-roles/`)
- `jq` installed for JSON processing
- Bash 4+ for script compatibility

### Step 1: Analyze Current State
```bash
cd non-group-privileges
chmod +x *.sh

# Analyze all users to understand current configurations
./analyze-iam-users.sh
```

This will generate:
- Detailed analysis report with user categorization
- Summary CSV with recommendations
- Statistics on user types and policy patterns

### Step 2: Plan Group Structure
Review the analysis reports to understand:
- Common permission patterns
- User categories and their needs
- Potential group consolidation opportunities

### Step 3: Test Remediation (Dry Run)
```bash
# Test remediation on all accounts (dry run mode)
./remediate-group-access.sh

# Test on specific account only
./remediate-group-access.sh --account ************

# Test on specific user only
./remediate-group-access.sh --account ************ --user clearml-ci-s3-read
```

### Step 4: Execute Remediation
```bash
# Execute remediation on development account first
./remediate-group-access.sh --execute --account ************

# After testing, execute on all accounts
./remediate-group-access.sh --execute --force
```

### Step 5: Validate Compliance
```bash
# Validate that all users are now compliant
./validate-group-access.sh
```

## Proposed Group Structure

Based on the user analysis, the following groups will be created:

### Service Account Groups
- **GitHubServiceAccounts** - For github-* users (CI/CD automation)
- **KeycloakServiceAccounts** - For keycloak-* users (Identity management)
- **TerraformServiceAccounts** - For terraform service users (Infrastructure)
- **MLServiceAccounts** - For clearml-* users (Machine learning)
- **VonageServiceAccounts** - For tokbox_vonage_* users (Video services)
- **DRPServiceAccounts** - For drp-* users (Disaster recovery)

### Human User Groups
- **DeveloperAccess** - For human users (developers/admins)
- **CustomAccess-{username}** - For users with unique requirements

## Safety Features

### Backup and Recovery
- All user configurations are backed up before changes
- Backup files include user details, policies, and group memberships
- Rollback procedures documented for emergency recovery

### Dry Run Mode
- Default mode is dry run - no changes made without explicit `--execute`
- Detailed preview of all planned changes
- Account and user filtering for targeted testing

### Cross-Account Role Requirements
- Uses existing cross-account audit roles for secure access
- No permanent credentials stored or transmitted
- Temporary session tokens for each account access

## Monitoring and Compliance

### Ongoing Validation
- Run validation script monthly to ensure continued compliance
- Monitor for new users that bypass group-based access
- Set up AWS Config rules to prevent future violations

### DRATA Integration
- Remediation addresses specific DRATA Test-217 requirements
- Documentation supports compliance audit trails
- Reports provide evidence of remediation actions

## Troubleshooting

### Common Issues
1. **Cross-account role access denied**
   - Verify roles are deployed in target accounts
   - Check role trust relationships and permissions

2. **User not found errors**
   - User may have been deleted since DRATA scan
   - Check if user exists before remediation

3. **Group creation failures**
   - Check for existing groups with same names
   - Verify IAM permissions for group management

### Support
- Review script logs in report directories
- Check AWS CloudTrail for detailed API call logs
- Validate cross-account role configuration

## Next Steps After Remediation

1. **Update IAM policies** to prevent future direct user permissions
2. **Implement AWS Config rules** for ongoing compliance monitoring
3. **Document group management procedures** for new user onboarding
4. **Train teams** on group-based access control best practices
5. **Schedule regular compliance validation** runs
