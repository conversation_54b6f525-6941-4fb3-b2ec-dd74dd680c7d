"Provider Name","Client ID or Client Alias","Resource ARN","Org Unit ID","Account ID","Account Name","Resource Name","Failing Message","Associated Help Article"
"AWS Organizational Units","************","arn:aws:iam::************:user/clearml-ci-s3-read","ou-3g6q-xym2ltt7","************","aws-account-ml","clearml-ci-s3-read","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/clearml-s3-access","ou-3g6q-xym2ltt7","************","aws-account-ml","clearml-s3-access","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/clearml-support","ou-3g6q-xym2ltt7","************","aws-account-ml","clearml-support","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/pavel.arzhaev","ou-3g6q-xym2ltt7","************","aws-account-ml","pavel.arzhaev","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/github-terraform-serv-acc","ou-3g6q-z5qego1j","************","<EMAIL>","github-terraform-serv-acc","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/keycloak-sxdb5pw5-sa","ou-3g6q-z5qego1j","************","<EMAIL>","keycloak-sxdb5pw5-sa","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/tokbox_vonage_integration","ou-3g6q-z5qego1j","************","<EMAIL>","tokbox_vonage_integration","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::033150149688:user/backend-monitoring-instance","ou-3g6q-z5qego1j","033150149688","Incode Technologies T2 9688","backend-monitoring-instance","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::033150149688:user/business-intelligence-analytics","ou-3g6q-z5qego1j","033150149688","Incode Technologies T2 9688","business-intelligence-analytics","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::033150149688:user/drp-failover-manager","ou-3g6q-z5qego1j","033150149688","Incode Technologies T2 9688","drp-failover-manager","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::033150149688:user/github-actions-id-ocr","ou-3g6q-z5qego1j","033150149688","Incode Technologies T2 9688","github-actions-id-ocr","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::033150149688:user/github-actions-keycloak-ecr","ou-3g6q-z5qego1j","033150149688","Incode Technologies T2 9688","github-actions-keycloak-ecr","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::033150149688:user/github-actions-ml-wasm-kit","ou-3g6q-z5qego1j","033150149688","Incode Technologies T2 9688","github-actions-ml-wasm-kit","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::033150149688:user/github-incode-api-official","ou-3g6q-z5qego1j","033150149688","Incode Technologies T2 9688","github-incode-api-official","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::033150149688:user/github-terraform-serv-acc","ou-3g6q-z5qego1j","033150149688","Incode Technologies T2 9688","github-terraform-serv-acc","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::033150149688:user/incode-s3","ou-3g6q-z5qego1j","033150149688","Incode Technologies T2 9688","incode-s3","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::033150149688:user/label-studio-admin","ou-3g6q-z5qego1j","033150149688","Incode Technologies T2 9688","label-studio-admin","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::033150149688:user/label-studio-task-user","ou-3g6q-z5qego1j","033150149688","Incode Technologies T2 9688","label-studio-task-user","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::033150149688:user/mario.dominguez","ou-3g6q-z5qego1j","033150149688","Incode Technologies T2 9688","mario.dominguez","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::033150149688:user/marygold537","ou-3g6q-z5qego1j","033150149688","Incode Technologies T2 9688","marygold537","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::033150149688:user/tokbox_vonage_integration_saas-eu","ou-3g6q-z5qego1j","033150149688","Incode Technologies T2 9688","tokbox_vonage_integration_saas-eu","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/terraform-saas-uae","ou-3g6q-z5qego1j","************","<EMAIL>","terraform-saas-uae","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/github-actions-web","ou-3g6q-z5qego1j","************","AWS Account Canada SAAS","github-actions-web","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/github-terraform-serv-acc","ou-3g6q-z5qego1j","************","AWS Account Canada SAAS","github-terraform-serv-acc","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/IncodeuserServiceSAAS","ou-3g6q-z5qego1j","************","AWS Account Canada SAAS","IncodeuserServiceSAAS","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/ServiceUserAccountInfoInfo","ou-3g6q-z5qego1j","************","AWS Account Canada SAAS","ServiceUserAccountInfoInfo","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/tokbox_vonage_integrationCanadaSAAS","ou-3g6q-z5qego1j","************","AWS Account Canada SAAS","tokbox_vonage_integrationCanadaSAAS","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/keycloak-3b4ss0st-sa","ou-3g6q-z5qego1j","************","<EMAIL>","keycloak-3b4ss0st-sa","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/keycloak-ast4obfh-sa","ou-3g6q-z5qego1j","************","<EMAIL>","keycloak-ast4obfh-sa","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/Administrator","ou-3g6q-z5qego1j","************","Microformas_Citi","Administrator","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/carlos.gutierrez","ou-3g6q-z5qego1j","************","Microformas_Citi","carlos.gutierrez","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/city-encryptedOnboardingFiles-user","ou-3g6q-z5qego1j","************","Microformas_Citi","city-encryptedOnboardingFiles-user","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/gerson.garcia","ou-3g6q-z5qego1j","************","Microformas_Citi","gerson.garcia","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/github-actions-terraform-account","ou-3g6q-z5qego1j","************","AWS-account-omni-shared-services","github-actions-terraform-account","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/github-terraform-serv-acc","ou-3g6q-z5qego1j","************","aws-account-demo-k8s","github-terraform-serv-acc","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/drp-failover-manager","ou-3g6q-z5qego1j","************","<EMAIL>","drp-failover-manager","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/github-terraform-serv-acc","ou-3g6q-z5qego1j","************","<EMAIL>","github-terraform-serv-acc","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/tokbox_vonage_integration","ou-3g6q-z5qego1j","************","<EMAIL>","tokbox_vonage_integration","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::637423257984:user/keycloak-77h3o78b-sa","ou-3g6q-z5qego1j","637423257984","<EMAIL>","keycloak-77h3o78b-sa","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::637423257984:user/keycloak-ygp3jid3-sa","ou-3g6q-z5qego1j","637423257984","<EMAIL>","keycloak-ygp3jid3-sa","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::637423257984:user/mkamal","ou-3g6q-z5qego1j","637423257984","<EMAIL>","mkamal","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/github-terraform-serv-acc","ou-3g6q-z5qego1j","************","<EMAIL>","github-terraform-serv-acc","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/keycloak-4bqtydgz-sa","ou-3g6q-z5qego1j","************","<EMAIL>","keycloak-4bqtydgz-sa","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/tokbox_vonage_integration","ou-3g6q-z5qego1j","************","<EMAIL>","tokbox_vonage_integration","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/github-terraform-serv-acc","ou-3g6q-z5qego1j","************","aws-account-citimx-prod","github-terraform-serv-acc","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/keycloak-nxjs2rt2-sa","ou-3g6q-z5qego1j","************","aws-account-citimx-prod","keycloak-nxjs2rt2-sa","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/tokbox_vonage_integrationCitimxprod","ou-3g6q-z5qego1j","************","aws-account-citimx-prod","tokbox_vonage_integrationCitimxprod","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/github-terraform-serv-acc","ou-3g6q-z5qego1j","************","aws-account-canada-k8s","github-terraform-serv-acc","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/keycloak-2ibazywi-sa","ou-3g6q-z5qego1j","************","aws-account-canada-k8s","keycloak-2ibazywi-sa","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/tokbox_vonage_integrationCanadaSAAS","ou-3g6q-z5qego1j","************","aws-account-canada-k8s","tokbox_vonage_integrationCanadaSAAS","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/github-actions-web","ou-3g6q-z5qego1j","************","AWS-account-saas-australia","github-actions-web","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
"AWS Organizational Units","************","arn:aws:iam::************:user/tokbox_vonage_integration_sydney","ou-3g6q-z5qego1j","************","AWS-account-saas-australia","tokbox_vonage_integration_sydney","You have AWS IAM users with an inline policy or direct policy attachment.","https://help.drata.com/en/articles/9828704-test-aws-iam-group-based-access-control"
