#!/bin/bash

# AWS IAM Group-Based Access Control Validation Script
# This script validates that users are properly configured with group-based access

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CSV_FILE="${SCRIPT_DIR}/Failing-Resources-For-Test-217-AWS-IAM-Group-Based-Access-Control-05302025.csv"
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
REPORTS_DIR="${SCRIPT_DIR}/iam-validation-reports"
REPORT_FILE="${REPORTS_DIR}/iam-validation-${TIMESTAMP}.txt"
SUMMARY_FILE="${REPORTS_DIR}/iam-validation-summary-${TIMESTAMP}.csv"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Create reports directory
mkdir -p "${REPORTS_DIR}"

# Initialize counters
total_users=0
compliant_users=0
non_compliant_users=0
failed_checks=0

echo -e "${BLUE}=== AWS IAM Group-Based Access Control Validation ===${NC}"
echo "Starting validation at $(date)"
echo

# Initialize report files
cat > "${REPORT_FILE}" << EOF
AWS IAM Group-Based Access Control Validation Report
Generated: $(date)
Source: ${CSV_FILE}

This report validates that IAM users are properly configured with group-based access control.

=== VALIDATION RESULTS ===

EOF

# Initialize summary CSV
cat > "${SUMMARY_FILE}" << EOF
Account ID,Account Name,User Name,Inline Policies,Attached Policies,Groups,Compliance Status,Issues
EOF

# Function to validate a single user
validate_user() {
    local account_id="$1"
    local account_name="$2"
    local username="$3"
    
    echo -e "${YELLOW}Validating user: ${username} in account ${account_id}${NC}"
    
    # Assume cross-account role
    local role_arn="arn:aws:iam::${account_id}:role/CrossAccountAuditRole"
    local assume_role_output
    
    if assume_role_output=$(aws sts assume-role --role-arn "$role_arn" --role-session-name "IAMValidation-$(date +%s)" 2>/dev/null); then
        # Extract credentials
        local access_key=$(echo "$assume_role_output" | jq -r '.Credentials.AccessKeyId')
        local secret_key=$(echo "$assume_role_output" | jq -r '.Credentials.SecretAccessKey')
        local session_token=$(echo "$assume_role_output" | jq -r '.Credentials.SessionToken')
        
        # Set temporary credentials
        export AWS_ACCESS_KEY_ID="$access_key"
        export AWS_SECRET_ACCESS_KEY="$secret_key"
        export AWS_SESSION_TOKEN="$session_token"
        
        # Get user details
        local user_info
        if user_info=$(aws iam get-user --user-name "$username" 2>/dev/null); then
            
            # Check inline policies
            local inline_policies_count=0
            local inline_policies_list=""
            while IFS= read -r policy_name; do
                if [[ -n "$policy_name" ]]; then
                    ((inline_policies_count++))
                    inline_policies_list="${inline_policies_list}${policy_name}; "
                fi
            done < <(aws iam list-user-policies --user-name "$username" 2>/dev/null | jq -r '.PolicyNames[]' 2>/dev/null || true)
            
            # Check attached managed policies
            local attached_policies_count=0
            local attached_policies_list=""
            while IFS= read -r policy_arn; do
                if [[ -n "$policy_arn" ]]; then
                    ((attached_policies_count++))
                    local policy_name=$(echo "$policy_arn" | awk -F'/' '{print $NF}')
                    attached_policies_list="${attached_policies_list}${policy_name}; "
                fi
            done < <(aws iam list-attached-user-policies --user-name "$username" 2>/dev/null | jq -r '.AttachedPolicies[].PolicyArn' 2>/dev/null || true)
            
            # Check groups
            local groups_count=0
            local groups_list=""
            while IFS= read -r group_name; do
                if [[ -n "$group_name" ]]; then
                    ((groups_count++))
                    groups_list="${groups_list}${group_name}; "
                fi
            done < <(aws iam get-groups-for-user --user-name "$username" 2>/dev/null | jq -r '.Groups[].GroupName' 2>/dev/null || true)
            
            # Determine compliance status
            local compliance_status="COMPLIANT"
            local issues=""
            
            if [[ $inline_policies_count -gt 0 ]]; then
                compliance_status="NON_COMPLIANT"
                issues="${issues}Has ${inline_policies_count} inline policies; "
                ((non_compliant_users++))
            fi
            
            if [[ $attached_policies_count -gt 0 ]]; then
                compliance_status="NON_COMPLIANT"
                issues="${issues}Has ${attached_policies_count} directly attached policies; "
                if [[ $inline_policies_count -eq 0 ]]; then
                    ((non_compliant_users++))
                fi
            fi
            
            if [[ $groups_count -eq 0 ]]; then
                if [[ "$compliance_status" == "COMPLIANT" ]]; then
                    compliance_status="WARNING"
                fi
                issues="${issues}Not member of any groups; "
            fi
            
            if [[ "$compliance_status" == "COMPLIANT" ]]; then
                ((compliant_users++))
                issues="None - user gets permissions through groups only"
            fi
            
            # Clean up issues string
            issues=$(echo "$issues" | sed 's/; $//')
            
            # Write detailed report
            cat >> "${REPORT_FILE}" << EOF

--- User: ${username} ---
Account: ${account_id} - ${account_name}
Compliance Status: ${compliance_status}

Inline Policies: ${inline_policies_count}
$([ $inline_policies_count -gt 0 ] && echo "  Policies: ${inline_policies_list}" || echo "  None")

Attached Managed Policies: ${attached_policies_count}
$([ $attached_policies_count -gt 0 ] && echo "  Policies: ${attached_policies_list}" || echo "  None")

Group Memberships: ${groups_count}
$([ $groups_count -gt 0 ] && echo "  Groups: ${groups_list}" || echo "  None")

Issues: ${issues}

EOF
            
            # Write to summary CSV
            echo "${account_id},\"${account_name}\",${username},${inline_policies_count},${attached_policies_count},${groups_count},${compliance_status},\"${issues}\"" >> "${SUMMARY_FILE}"
            
            # Color-coded output
            case "$compliance_status" in
                "COMPLIANT")
                    echo -e "${GREEN}✓ COMPLIANT - ${username}${NC}"
                    ;;
                "NON_COMPLIANT")
                    echo -e "${RED}✗ NON_COMPLIANT - ${username}: ${issues}${NC}"
                    ;;
                "WARNING")
                    echo -e "${YELLOW}⚠ WARNING - ${username}: ${issues}${NC}"
                    ;;
            esac
            
        else
            echo -e "${RED}✗ Failed to get user details for ${username}${NC}"
            echo "${account_id},\"${account_name}\",${username},UNKNOWN,UNKNOWN,UNKNOWN,FAILED,\"Could not retrieve user details\"" >> "${SUMMARY_FILE}"
            ((failed_checks++))
        fi
        
        # Clear temporary credentials
        unset AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN
        
    else
        echo -e "${RED}✗ Failed to assume role in account ${account_id}${NC}"
        echo "${account_id},\"${account_name}\",${username},UNKNOWN,UNKNOWN,UNKNOWN,NO_ACCESS,\"Could not assume cross-account role\"" >> "${SUMMARY_FILE}"
        ((failed_checks++))
    fi
}

# Main processing loop
echo -e "${BLUE}Processing users from CSV file...${NC}"

# Process each line from CSV
tail -n +2 "$CSV_FILE" | while IFS=',' read -r provider_name client_id resource_arn org_unit_id account_id account_name resource_name failing_message help_article; do
    # Clean up quoted fields
    account_id=$(echo "$account_id" | tr -d '"')
    account_name=$(echo "$account_name" | tr -d '"')
    resource_name=$(echo "$resource_name" | tr -d '"')
    
    # Skip empty lines
    [[ -z "$account_id" ]] && continue
    
    ((total_users++))
    
    validate_user "$account_id" "$account_name" "$resource_name"
    echo
done

# Generate final statistics
cat >> "${REPORT_FILE}" << EOF

=== VALIDATION SUMMARY ===
Total Users Checked: ${total_users}
Compliant Users: ${compliant_users}
Non-Compliant Users: ${non_compliant_users}
Failed Checks: ${failed_checks}

Compliance Rate: $(( compliant_users * 100 / (total_users - failed_checks) ))%

=== RECOMMENDATIONS ===
EOF

if [[ $non_compliant_users -gt 0 ]]; then
    cat >> "${REPORT_FILE}" << EOF

Non-compliant users still have inline policies or direct policy attachments.
These users need to be migrated to group-based access control:

1. Review the detailed report above for specific issues
2. Use the remediate-group-access.sh script to fix non-compliant users
3. Test group-based permissions before removing direct permissions
4. Re-run this validation script after remediation

EOF
else
    cat >> "${REPORT_FILE}" << EOF

All users are compliant with group-based access control requirements!
No further action needed for DRATA compliance.

EOF
fi

echo -e "${GREEN}=== Validation Complete ===${NC}"
echo "Total users validated: ${total_users}"
echo "Compliant users: ${compliant_users}"
echo "Non-compliant users: ${non_compliant_users}"
echo "Failed checks: ${failed_checks}"

if [[ $total_users -gt $failed_checks ]]; then
    local compliance_rate=$(( compliant_users * 100 / (total_users - failed_checks) ))
    echo "Compliance rate: ${compliance_rate}%"
fi

echo
echo "Reports generated:"
echo "  - Detailed report: ${REPORT_FILE}"
echo "  - Summary CSV: ${SUMMARY_FILE}"

if [[ $non_compliant_users -gt 0 ]]; then
    echo
    echo -e "${YELLOW}Next steps:${NC}"
    echo "1. Review non-compliant users in the detailed report"
    echo "2. Run remediate-group-access.sh to fix issues"
    echo "3. Re-run this validation script to confirm compliance"
else
    echo
    echo -e "${GREEN}All users are compliant with group-based access control!${NC}"
fi
