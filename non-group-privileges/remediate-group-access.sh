#!/bin/bash

# AWS IAM Group-Based Access Control Remediation Script
# This script migrates IAM users from inline/direct policies to group-based access

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CSV_FILE="${SCRIPT_DIR}/Failing-Resources-For-Test-217-AWS-IAM-Group-Based-Access-Control-********.csv"
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
REPORTS_DIR="${SCRIPT_DIR}/iam-remediation-reports"
REPORT_FILE="${REPORTS_DIR}/iam-remediation-${TIMESTAMP}.txt"
BACKUP_FILE="${REPORTS_DIR}/iam-backup-${TIMESTAMP}.json"
SUMMARY_FILE="${REPORTS_DIR}/iam-remediation-summary-${TIMESTAMP}.csv"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Command line options
DRY_RUN=true
SPECIFIC_ACCOUNT=""
SPECIFIC_USER=""
FORCE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --execute)
            DRY_RUN=false
            shift
            ;;
        --account)
            SPECIFIC_ACCOUNT="$2"
            shift 2
            ;;
        --user)
            SPECIFIC_USER="$2"
            shift 2
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --execute          Actually perform the remediation (default: dry-run)"
            echo "  --account ID       Only process specific account ID"
            echo "  --user NAME        Only process specific user name"
            echo "  --force            Skip confirmation prompts"
            echo "  --help             Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Create reports directory
mkdir -p "${REPORTS_DIR}"

echo -e "${BLUE}=== AWS IAM Group-Based Access Control Remediation ===${NC}"
echo "Mode: $([ "$DRY_RUN" = true ] && echo "DRY RUN" || echo "EXECUTE")"
echo "Starting remediation at $(date)"
echo

if [ "$DRY_RUN" = true ]; then
    echo -e "${YELLOW}Running in DRY RUN mode - no changes will be made${NC}"
    echo "Use --execute to perform actual remediation"
    echo
fi

# Initialize report files
cat > "${REPORT_FILE}" << EOF
AWS IAM Group-Based Access Control Remediation Report
Generated: $(date)
Mode: $([ "$DRY_RUN" = true ] && echo "DRY RUN" || echo "EXECUTE")

=== REMEDIATION ACTIONS ===

EOF

# Initialize summary CSV
cat > "${SUMMARY_FILE}" << EOF
Account ID,Account Name,User Name,Action,Group Created,Policies Migrated,Status,Details
EOF

# Initialize backup file
echo "{" > "${BACKUP_FILE}"
echo "  \"timestamp\": \"$(date -Iseconds)\"," >> "${BACKUP_FILE}"
echo "  \"backups\": [" >> "${BACKUP_FILE}"

# Function to create IAM group based on user type and policies
create_iam_group() {
    local account_id="$1"
    local username="$2"
    local user_type="$3"
    local policies="$4"
    
    # Generate group name based on user type
    local group_name
    case "$user_type" in
        "github-service")
            group_name="GitHubServiceAccounts"
            ;;
        "keycloak-service")
            group_name="KeycloakServiceAccounts"
            ;;
        "terraform-service")
            group_name="TerraformServiceAccounts"
            ;;
        "ml-service")
            group_name="MLServiceAccounts"
            ;;
        "vonage-service")
            group_name="VonageServiceAccounts"
            ;;
        "drp-service")
            group_name="DRPServiceAccounts"
            ;;
        "service-account")
            group_name="GeneralServiceAccounts"
            ;;
        "human-user")
            group_name="DeveloperAccess"
            ;;
        *)
            group_name="CustomAccess-${username}"
            ;;
    esac
    
    echo "$group_name"
}

# Function to backup user configuration
backup_user_config() {
    local account_id="$1"
    local username="$2"
    
    echo "    {" >> "${BACKUP_FILE}"
    echo "      \"account_id\": \"${account_id}\"," >> "${BACKUP_FILE}"
    echo "      \"username\": \"${username}\"," >> "${BACKUP_FILE}"
    echo "      \"timestamp\": \"$(date -Iseconds)\"," >> "${BACKUP_FILE}"
    
    # Get user details
    local user_info=$(aws iam get-user --user-name "$username" 2>/dev/null || echo "{}")
    echo "      \"user_info\": ${user_info}," >> "${BACKUP_FILE}"
    
    # Get inline policies
    local inline_policies=$(aws iam list-user-policies --user-name "$username" 2>/dev/null || echo '{"PolicyNames":[]}')
    echo "      \"inline_policies\": ${inline_policies}," >> "${BACKUP_FILE}"
    
    # Get inline policy documents
    echo "      \"inline_policy_documents\": [" >> "${BACKUP_FILE}"
    local first_policy=true
    while IFS= read -r policy_name; do
        if [[ -n "$policy_name" ]]; then
            if [ "$first_policy" = false ]; then
                echo "," >> "${BACKUP_FILE}"
            fi
            local policy_doc=$(aws iam get-user-policy --user-name "$username" --policy-name "$policy_name" 2>/dev/null || echo "{}")
            echo "        {\"name\": \"${policy_name}\", \"document\": ${policy_doc}}" >> "${BACKUP_FILE}"
            first_policy=false
        fi
    done < <(echo "$inline_policies" | jq -r '.PolicyNames[]' 2>/dev/null || true)
    echo "      ]," >> "${BACKUP_FILE}"
    
    # Get attached managed policies
    local attached_policies=$(aws iam list-attached-user-policies --user-name "$username" 2>/dev/null || echo '{"AttachedPolicies":[]}')
    echo "      \"attached_policies\": ${attached_policies}," >> "${BACKUP_FILE}"
    
    # Get current groups
    local groups=$(aws iam get-groups-for-user --user-name "$username" 2>/dev/null || echo '{"Groups":[]}')
    echo "      \"current_groups\": ${groups}" >> "${BACKUP_FILE}"
    echo "    }," >> "${BACKUP_FILE}"
}

# Function to remediate a single user
remediate_user() {
    local account_id="$1"
    local account_name="$2"
    local username="$3"
    
    echo -e "${YELLOW}Processing user: ${username} in account ${account_id}${NC}"
    
    # Skip if specific account/user filters are set
    if [[ -n "$SPECIFIC_ACCOUNT" && "$account_id" != "$SPECIFIC_ACCOUNT" ]]; then
        echo "Skipping - not target account"
        return
    fi
    
    if [[ -n "$SPECIFIC_USER" && "$username" != "$SPECIFIC_USER" ]]; then
        echo "Skipping - not target user"
        return
    fi
    
    # Assume cross-account role
    local role_arn="arn:aws:iam::${account_id}:role/CrossAccountAuditRole"
    local assume_role_output
    
    if assume_role_output=$(aws sts assume-role --role-arn "$role_arn" --role-session-name "IAMRemediation-$(date +%s)" 2>/dev/null); then
        # Extract credentials
        local access_key=$(echo "$assume_role_output" | jq -r '.Credentials.AccessKeyId')
        local secret_key=$(echo "$assume_role_output" | jq -r '.Credentials.SecretAccessKey')
        local session_token=$(echo "$assume_role_output" | jq -r '.Credentials.SessionToken')
        
        # Set temporary credentials
        export AWS_ACCESS_KEY_ID="$access_key"
        export AWS_SECRET_ACCESS_KEY="$secret_key"
        export AWS_SESSION_TOKEN="$session_token"
        
        # Backup current configuration
        backup_user_config "$account_id" "$username"
        
        # Get user details
        local user_info
        if user_info=$(aws iam get-user --user-name "$username" 2>/dev/null); then
            
            # Categorize user type
            local user_type
            if [[ "$username" =~ ^github- ]]; then
                user_type="github-service"
            elif [[ "$username" =~ ^keycloak- ]]; then
                user_type="keycloak-service"
            elif [[ "$username" =~ terraform ]]; then
                user_type="terraform-service"
            elif [[ "$username" =~ ^clearml- ]]; then
                user_type="ml-service"
            elif [[ "$username" =~ ^tokbox_vonage ]]; then
                user_type="vonage-service"
            elif [[ "$username" =~ ^drp- ]]; then
                user_type="drp-service"
            elif [[ "$username" =~ -sa$ ]]; then
                user_type="service-account"
            elif [[ "$username" =~ \. ]]; then
                user_type="human-user"
            else
                user_type="other"
            fi
            
            # Get current policies
            local inline_policies=$(aws iam list-user-policies --user-name "$username" 2>/dev/null | jq -r '.PolicyNames[]' | wc -l)
            local attached_policies=$(aws iam list-attached-user-policies --user-name "$username" 2>/dev/null | jq -r '.AttachedPolicies[].PolicyArn' | wc -l)
            
            # Determine group name
            local group_name=$(create_iam_group "$account_id" "$username" "$user_type" "")
            
            echo "  User type: $user_type"
            echo "  Proposed group: $group_name"
            echo "  Inline policies: $inline_policies"
            echo "  Attached policies: $attached_policies"
            
            # Create group if it doesn't exist
            local group_created=false
            if ! aws iam get-group --group-name "$group_name" >/dev/null 2>&1; then
                echo "  Creating group: $group_name"
                if [ "$DRY_RUN" = false ]; then
                    aws iam create-group --group-name "$group_name" --path "/"
                fi
                group_created=true
            else
                echo "  Group already exists: $group_name"
            fi
            
            # Migrate inline policies to group
            local policies_migrated=0
            if [[ $inline_policies -gt 0 ]]; then
                while IFS= read -r policy_name; do
                    if [[ -n "$policy_name" ]]; then
                        echo "    Migrating inline policy: $policy_name"
                        if [ "$DRY_RUN" = false ]; then
                            # Get policy document
                            local policy_doc=$(aws iam get-user-policy --user-name "$username" --policy-name "$policy_name" | jq -c '.PolicyDocument')
                            
                            # Create group policy with same name
                            aws iam put-group-policy --group-name "$group_name" --policy-name "${policy_name}" --policy-document "$policy_doc"
                            
                            # Remove from user
                            aws iam delete-user-policy --user-name "$username" --policy-name "$policy_name"
                        fi
                        ((policies_migrated++))
                    fi
                done < <(aws iam list-user-policies --user-name "$username" 2>/dev/null | jq -r '.PolicyNames[]')
            fi
            
            # Migrate attached managed policies to group
            if [[ $attached_policies -gt 0 ]]; then
                while IFS= read -r policy_arn; do
                    if [[ -n "$policy_arn" ]]; then
                        echo "    Migrating attached policy: $policy_arn"
                        if [ "$DRY_RUN" = false ]; then
                            # Attach to group
                            aws iam attach-group-policy --group-name "$group_name" --policy-arn "$policy_arn"
                            
                            # Detach from user
                            aws iam detach-user-policy --user-name "$username" --policy-arn "$policy_arn"
                        fi
                        ((policies_migrated++))
                    fi
                done < <(aws iam list-attached-user-policies --user-name "$username" 2>/dev/null | jq -r '.AttachedPolicies[].PolicyArn')
            fi
            
            # Add user to group
            echo "    Adding user to group: $group_name"
            if [ "$DRY_RUN" = false ]; then
                aws iam add-user-to-group --group-name "$group_name" --user-name "$username"
            fi
            
            # Write to report
            cat >> "${REPORT_FILE}" << EOF

--- User: ${username} ---
Account: ${account_id} - ${account_name}
User Type: ${user_type}
Group: ${group_name}
Group Created: ${group_created}
Policies Migrated: ${policies_migrated}
Status: $([ "$DRY_RUN" = true ] && echo "DRY RUN" || echo "SUCCESS")

EOF
            
            # Write to summary
            echo "${account_id},\"${account_name}\",${username},MIGRATE_TO_GROUP,${group_created},${policies_migrated},$([ "$DRY_RUN" = true ] && echo "DRY_RUN" || echo "SUCCESS"),\"Group: ${group_name}\"" >> "${SUMMARY_FILE}"
            
            echo -e "${GREEN}✓ Successfully processed ${username}${NC}"
            
        else
            echo -e "${RED}✗ Failed to get user details for ${username}${NC}"
            echo "${account_id},\"${account_name}\",${username},FAILED,false,0,ERROR,\"Could not retrieve user details\"" >> "${SUMMARY_FILE}"
        fi
        
        # Clear temporary credentials
        unset AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN
        
    else
        echo -e "${RED}✗ Failed to assume role in account ${account_id}${NC}"
        echo "${account_id},\"${account_name}\",${username},FAILED,false,0,NO_ACCESS,\"Could not assume cross-account role\"" >> "${SUMMARY_FILE}"
    fi
}

# Confirmation prompt
if [ "$DRY_RUN" = false ] && [ "$FORCE" = false ]; then
    echo -e "${RED}WARNING: This will modify IAM permissions across multiple accounts!${NC}"
    echo "This action will:"
    echo "  - Create IAM groups based on user types"
    echo "  - Move inline and attached policies to groups"
    echo "  - Add users to appropriate groups"
    echo "  - Remove direct policy attachments from users"
    echo
    read -p "Are you sure you want to continue? (yes/no): " confirm
    if [[ "$confirm" != "yes" ]]; then
        echo "Remediation cancelled."
        exit 0
    fi
fi

# Process users from CSV
echo -e "${BLUE}Processing users from CSV file...${NC}"
total_users=0

tail -n +2 "$CSV_FILE" | while IFS=',' read -r provider_name client_id resource_arn org_unit_id account_id account_name resource_name failing_message help_article; do
    # Clean up quoted fields
    account_id=$(echo "$account_id" | tr -d '"')
    account_name=$(echo "$account_name" | tr -d '"')
    resource_name=$(echo "$resource_name" | tr -d '"')
    
    # Skip empty lines
    [[ -z "$account_id" ]] && continue
    
    ((total_users++))
    
    remediate_user "$account_id" "$account_name" "$resource_name"
    echo
done

# Close backup file
sed -i '$ s/,$//' "${BACKUP_FILE}" 2>/dev/null || true
echo "  ]" >> "${BACKUP_FILE}"
echo "}" >> "${BACKUP_FILE}"

echo -e "${GREEN}=== Remediation Complete ===${NC}"
echo "Total users processed: ${total_users}"
echo "Mode: $([ "$DRY_RUN" = true ] && echo "DRY RUN" || echo "EXECUTE")"
echo
echo "Reports generated:"
echo "  - Detailed report: ${REPORT_FILE}"
echo "  - Summary CSV: ${SUMMARY_FILE}"
echo "  - Backup file: ${BACKUP_FILE}"
