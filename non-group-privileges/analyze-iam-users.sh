#!/bin/bash

# AWS IAM Group-Based Access Control Analysis Script
# This script analyzes IAM users with inline policies or direct policy attachments
# to determine feasibility of migrating to group-based access control

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CSV_FILE="${SCRIPT_DIR}/Failing-Resources-For-Test-217-AWS-IAM-Group-Based-Access-Control-********.csv"
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
REPORTS_DIR="${SCRIPT_DIR}/iam-analysis-reports"
REPORT_FILE="${REPORTS_DIR}/iam-user-analysis-${TIMESTAMP}.txt"
SUMMARY_FILE="${REPORTS_DIR}/iam-user-summary-${TIMESTAMP}.csv"
STATS_FILE="${REPORTS_DIR}/iam-user-stats-${TIMESTAMP}.txt"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Create reports directory
mkdir -p "${REPORTS_DIR}"

# Initialize counters
total_users=0
service_accounts=0
human_users=0
github_users=0
keycloak_users=0
terraform_users=0
other_users=0
accounts_processed=0
failed_accounts=0

echo -e "${BLUE}=== AWS IAM Group-Based Access Control Analysis ===${NC}"
echo "Starting analysis at $(date)"
echo "Report will be saved to: ${REPORT_FILE}"
echo

# Initialize report files
cat > "${REPORT_FILE}" << EOF
AWS IAM Group-Based Access Control Analysis Report
Generated: $(date)
Source: ${CSV_FILE}

This report analyzes IAM users with inline policies or direct policy attachments
to determine the feasibility of migrating to group-based access control.

=== ANALYSIS RESULTS ===

EOF

# Initialize summary CSV
cat > "${SUMMARY_FILE}" << EOF
Account ID,Account Name,User Name,User Type,Inline Policies,Attached Policies,Groups,Analysis Status,Recommendation
EOF

# Function to categorize user type
categorize_user() {
    local username="$1"
    
    if [[ "$username" =~ ^github- ]]; then
        echo "github-service"
    elif [[ "$username" =~ ^keycloak- ]]; then
        echo "keycloak-service"
    elif [[ "$username" =~ terraform ]]; then
        echo "terraform-service"
    elif [[ "$username" =~ ^clearml- ]]; then
        echo "ml-service"
    elif [[ "$username" =~ ^tokbox_vonage ]]; then
        echo "vonage-service"
    elif [[ "$username" =~ ^drp- ]]; then
        echo "drp-service"
    elif [[ "$username" =~ -sa$ ]]; then
        echo "service-account"
    elif [[ "$username" =~ \. ]]; then
        echo "human-user"
    else
        echo "other"
    fi
}

# Function to analyze a single IAM user
analyze_user() {
    local account_id="$1"
    local account_name="$2"
    local username="$3"
    
    echo -e "${YELLOW}Analyzing user: ${username} in account ${account_id} (${account_name})${NC}"
    
    # Assume cross-account role if available
    local role_arn="arn:aws:iam::${account_id}:role/CrossAccountAuditRole"
    local assume_role_output
    
    if assume_role_output=$(aws sts assume-role --role-arn "$role_arn" --role-session-name "IAMAnalysis-$(date +%s)" 2>/dev/null); then
        # Extract credentials
        local access_key=$(echo "$assume_role_output" | jq -r '.Credentials.AccessKeyId')
        local secret_key=$(echo "$assume_role_output" | jq -r '.Credentials.SecretAccessKey')
        local session_token=$(echo "$assume_role_output" | jq -r '.Credentials.SessionToken')
        
        # Set temporary credentials
        export AWS_ACCESS_KEY_ID="$access_key"
        export AWS_SECRET_ACCESS_KEY="$secret_key"
        export AWS_SESSION_TOKEN="$session_token"
        
        # Get user details
        local user_info
        if user_info=$(aws iam get-user --user-name "$username" 2>/dev/null); then
            local user_arn=$(echo "$user_info" | jq -r '.User.Arn')
            local create_date=$(echo "$user_info" | jq -r '.User.CreateDate')
            
            # Get inline policies
            local inline_policies
            inline_policies=$(aws iam list-user-policies --user-name "$username" 2>/dev/null | jq -r '.PolicyNames[]' | wc -l)
            
            # Get attached managed policies
            local attached_policies
            attached_policies=$(aws iam list-attached-user-policies --user-name "$username" 2>/dev/null | jq -r '.AttachedPolicies[].PolicyName' | wc -l)
            
            # Get groups
            local groups
            groups=$(aws iam get-groups-for-user --user-name "$username" 2>/dev/null | jq -r '.Groups[].GroupName' | tr '\n' ';' | sed 's/;$//')
            
            # Get detailed policy information
            local inline_policy_details=""
            local attached_policy_details=""
            
            # List inline policies with details
            if [[ $inline_policies -gt 0 ]]; then
                while IFS= read -r policy_name; do
                    if [[ -n "$policy_name" ]]; then
                        local policy_doc=$(aws iam get-user-policy --user-name "$username" --policy-name "$policy_name" 2>/dev/null | jq -r '.PolicyDocument')
                        inline_policy_details="${inline_policy_details}${policy_name}: ${policy_doc}\n"
                    fi
                done < <(aws iam list-user-policies --user-name "$username" 2>/dev/null | jq -r '.PolicyNames[]')
            fi
            
            # List attached managed policies with details
            if [[ $attached_policies -gt 0 ]]; then
                while IFS= read -r policy_arn; do
                    if [[ -n "$policy_arn" ]]; then
                        local policy_name=$(echo "$policy_arn" | awk -F'/' '{print $NF}')
                        attached_policy_details="${attached_policy_details}${policy_name} (${policy_arn})\n"
                    fi
                done < <(aws iam list-attached-user-policies --user-name "$username" 2>/dev/null | jq -r '.AttachedPolicies[].PolicyArn')
            fi
            
            # Categorize user
            local user_type=$(categorize_user "$username")
            
            # Determine recommendation
            local recommendation="CREATE_GROUP"
            if [[ $inline_policies -eq 0 && $attached_policies -eq 1 ]]; then
                recommendation="SIMPLE_GROUP_MIGRATION"
            elif [[ $inline_policies -gt 0 && $attached_policies -eq 0 ]]; then
                recommendation="INLINE_TO_GROUP_MIGRATION"
            elif [[ $inline_policies -gt 0 && $attached_policies -gt 0 ]]; then
                recommendation="COMPLEX_MIGRATION"
            fi
            
            # Write to report
            cat >> "${REPORT_FILE}" << EOF

--- User: ${username} (${user_type}) ---
Account: ${account_id} - ${account_name}
ARN: ${user_arn}
Created: ${create_date}
Inline Policies: ${inline_policies}
Attached Policies: ${attached_policies}
Groups: ${groups:-"None"}
Recommendation: ${recommendation}

Inline Policy Details:
${inline_policy_details:-"None"}

Attached Policy Details:
${attached_policy_details:-"None"}

EOF
            
            # Write to summary CSV
            echo "${account_id},\"${account_name}\",${username},${user_type},${inline_policies},${attached_policies},\"${groups}\",SUCCESS,${recommendation}" >> "${SUMMARY_FILE}"
            
            # Update counters
            case "$user_type" in
                "github-service") ((github_users++)) ;;
                "keycloak-service") ((keycloak_users++)) ;;
                "terraform-service") ((terraform_users++)) ;;
                "service-account") ((service_accounts++)) ;;
                "human-user") ((human_users++)) ;;
                *) ((other_users++)) ;;
            esac
            
            echo -e "${GREEN}✓ Successfully analyzed ${username}${NC}"
            
        else
            echo -e "${RED}✗ Failed to get user details for ${username}${NC}"
            echo "${account_id},\"${account_name}\",${username},UNKNOWN,0,0,\"\",FAILED,MANUAL_REVIEW" >> "${SUMMARY_FILE}"
        fi
        
        # Clear temporary credentials
        unset AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN
        
    else
        echo -e "${RED}✗ Failed to assume role in account ${account_id}${NC}"
        echo "${account_id},\"${account_name}\",${username},UNKNOWN,0,0,\"\",NO_ACCESS,MANUAL_REVIEW" >> "${SUMMARY_FILE}"
        ((failed_accounts++))
    fi
}

# Main processing loop
echo -e "${BLUE}Processing users from CSV file...${NC}"

# Skip header and process each line
tail -n +2 "$CSV_FILE" | while IFS=',' read -r provider_name client_id resource_arn org_unit_id account_id account_name resource_name failing_message help_article; do
    # Clean up quoted fields
    account_id=$(echo "$account_id" | tr -d '"')
    account_name=$(echo "$account_name" | tr -d '"')
    resource_name=$(echo "$resource_name" | tr -d '"')
    
    # Skip empty lines
    [[ -z "$account_id" ]] && continue
    
    ((total_users++))
    
    # Track unique accounts
    if ! grep -q "^${account_id}," "${SUMMARY_FILE}" 2>/dev/null; then
        ((accounts_processed++))
    fi
    
    # Analyze the user
    analyze_user "$account_id" "$account_name" "$resource_name"
    
    echo "Progress: ${total_users} users processed"
    echo
done

# Generate statistics
cat > "${STATS_FILE}" << EOF
AWS IAM Group-Based Access Control Analysis Statistics
Generated: $(date)

=== SUMMARY ===
Total Users Analyzed: ${total_users}
Accounts Processed: ${accounts_processed}
Failed Account Access: ${failed_accounts}

=== USER TYPE BREAKDOWN ===
GitHub Service Accounts: ${github_users}
Keycloak Service Accounts: ${keycloak_users}
Terraform Service Accounts: ${terraform_users}
Other Service Accounts: ${service_accounts}
Human Users: ${human_users}
Other/Unknown: ${other_users}

=== RECOMMENDATIONS ===
Review the summary CSV file for detailed recommendations:
${SUMMARY_FILE}

=== NEXT STEPS ===
1. Review the detailed analysis report: ${REPORT_FILE}
2. Examine the summary CSV for migration strategies: ${SUMMARY_FILE}
3. Create IAM groups for similar user types
4. Test group-based permissions in non-production accounts
5. Migrate users to groups and remove direct permissions

EOF

echo -e "${GREEN}=== Analysis Complete ===${NC}"
echo "Total users analyzed: ${total_users}"
echo "Reports generated:"
echo "  - Detailed report: ${REPORT_FILE}"
echo "  - Summary CSV: ${SUMMARY_FILE}"
echo "  - Statistics: ${STATS_FILE}"
echo
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Review the reports to understand current IAM configuration"
echo "2. Plan group creation strategy based on user types"
echo "3. Test group-based access in development accounts"
echo "4. Execute migration using the remediation script"
