#!/bin/bash

# Find and delete unused default VPCs across all accounts and regions
# This script assumes you have AWS CLI configured with appropriate cross-account access

# Note: We don't use 'set -e' here because we want to continue processing other accounts
# even if some accounts fail (e.g., suspended accounts, role assumption failures)

# Configuration
ROOT_OU_ID="r-3g6q"

# List of all enabled regions
ENABLED_REGIONS=(
    "ap-south-1"
    "eu-north-1"
    "eu-west-3"
    "eu-west-2"
    "eu-west-1"
    "ap-northeast-3"
    "ap-northeast-2"
    "ap-northeast-1"
    "ca-central-1"
    "sa-east-1"
    "ap-southeast-1"
    "ap-southeast-2"
    "eu-central-1"
    "ap-southeast-3"
    "us-east-1"
    "us-east-2"
    "us-west-1"
    "us-west-2"
)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to get all accounts in the organization
get_all_accounts() {
    aws organizations list-accounts \
        --query 'Accounts[?Status==`ACTIVE`].Id' \
        --output text
}

# Function to assume cross-account role
assume_role() {
    local account_id=$1
    local role_name="CrossAccountVPCManagementRole"
    local external_id="DrataCleanup2025"

    local role_arn="arn:aws:iam::${account_id}:role/${role_name}"

    # Verify we're in the management account before assuming role
    local current_account=$(aws sts get-caller-identity --query 'Account' --output text 2>/dev/null || echo "UNKNOWN")
    if [ "$current_account" != "$MANAGEMENT_ACCOUNT_ID" ]; then
        log "    ⚠️  Warning: Not in management account before assume role (currently in: $current_account)"
        log "    🔄 Resetting to original credentials..."
        reset_credentials
        current_account=$(aws sts get-caller-identity --query 'Account' --output text 2>/dev/null || echo "UNKNOWN")
        if [ "$current_account" != "$MANAGEMENT_ACCOUNT_ID" ]; then
            log "    ❌ Still not in management account after reset (currently in: $current_account)"
            return 1
        fi
    fi

    # Assume the role and export credentials
    local assume_result=$(aws sts assume-role \
        --role-arn "$role_arn" \
        --role-session-name "VPCCleanup-$(date +%s)" \
        --external-id "$external_id" \
        --duration-seconds 3600 2>/dev/null || echo "ERROR")

    if [ "$assume_result" = "ERROR" ]; then
        log "    ❌ STS assume-role command failed for $role_arn"
        return 1
    fi

    # Check if result contains valid JSON
    if ! echo "$assume_result" | jq . >/dev/null 2>&1; then
        log "    ❌ Invalid JSON response from assume-role"
        log "    Response: $assume_result"
        return 1
    fi

    # Export the temporary credentials
    export AWS_ACCESS_KEY_ID=$(echo "$assume_result" | jq -r '.Credentials.AccessKeyId')
    export AWS_SECRET_ACCESS_KEY=$(echo "$assume_result" | jq -r '.Credentials.SecretAccessKey')
    export AWS_SESSION_TOKEN=$(echo "$assume_result" | jq -r '.Credentials.SessionToken')

    # Verify the credentials work
    local assumed_account=$(aws sts get-caller-identity --query 'Account' --output text 2>/dev/null || echo "UNKNOWN")
    if [ "$assumed_account" != "$account_id" ]; then
        log "    ❌ Credential verification failed. Expected: $account_id, Got: $assumed_account"
        reset_credentials
        return 1
    fi

    return 0
}

# Store original credentials at script start
store_original_credentials() {
    ORIGINAL_AWS_ACCESS_KEY_ID="$AWS_ACCESS_KEY_ID"
    ORIGINAL_AWS_SECRET_ACCESS_KEY="$AWS_SECRET_ACCESS_KEY"
    ORIGINAL_AWS_SESSION_TOKEN="$AWS_SESSION_TOKEN"
    ORIGINAL_AWS_PROFILE="$AWS_PROFILE"
}

# Function to reset to original credentials
reset_credentials() {
    # Clear any assumed role credentials
    unset AWS_ACCESS_KEY_ID
    unset AWS_SECRET_ACCESS_KEY
    unset AWS_SESSION_TOKEN

    # Restore original credentials if they existed
    if [ -n "$ORIGINAL_AWS_ACCESS_KEY_ID" ]; then
        export AWS_ACCESS_KEY_ID="$ORIGINAL_AWS_ACCESS_KEY_ID"
    fi
    if [ -n "$ORIGINAL_AWS_SECRET_ACCESS_KEY" ]; then
        export AWS_SECRET_ACCESS_KEY="$ORIGINAL_AWS_SECRET_ACCESS_KEY"
    fi
    if [ -n "$ORIGINAL_AWS_SESSION_TOKEN" ]; then
        export AWS_SESSION_TOKEN="$ORIGINAL_AWS_SESSION_TOKEN"
    fi
    if [ -n "$ORIGINAL_AWS_PROFILE" ]; then
        export AWS_PROFILE="$ORIGINAL_AWS_PROFILE"
    fi
}

# Function to check if VPC is truly unused
is_vpc_unused() {
    local account_id=$1
    local region=$2
    local vpc_id=$3

    # Check for EC2 instances
    local instances=$(aws ec2 describe-instances \
        --region "$region" \
        --filters "Name=vpc-id,Values=$vpc_id" "Name=instance-state-name,Values=running,stopped,stopping,pending" \
        --query 'Reservations[*].Instances[*].InstanceId' \
        --output text 2>/dev/null || echo "")

    if [ -n "$instances" ]; then
        return 1  # VPC is in use
    fi

    # Check for RDS instances
    local rds_instances=$(aws rds describe-db-instances \
        --region "$region" \
        --query "DBInstances[?DBSubnetGroup.VpcId=='$vpc_id'].DBInstanceIdentifier" \
        --output text 2>/dev/null || echo "")

    if [ -n "$rds_instances" ]; then
        return 1  # VPC is in use
    fi

    # Check for Lambda functions (they create ENIs)
    local enis=$(aws ec2 describe-network-interfaces \
        --region "$region" \
        --filters "Name=vpc-id,Values=$vpc_id" \
        --query 'NetworkInterfaces[*].NetworkInterfaceId' \
        --output text 2>/dev/null || echo "")

    if [ -n "$enis" ]; then
        return 1  # VPC is in use
    fi

    # Check for ELB/ALB
    local elbs=$(aws elbv2 describe-load-balancers \
        --region "$region" \
        --query "LoadBalancers[?VpcId=='$vpc_id'].LoadBalancerArn" \
        --output text 2>/dev/null || echo "")

    if [ -n "$elbs" ]; then
        return 1  # VPC is in use
    fi

    # Check for Classic Load Balancers
    local classic_elbs=$(aws elb describe-load-balancers \
        --region "$region" \
        --query "LoadBalancerDescriptions[?VPCId=='$vpc_id'].LoadBalancerName" \
        --output text 2>/dev/null || echo "")

    if [ -n "$classic_elbs" ]; then
        return 1  # VPC is in use
    fi

    # Check for NAT Gateways
    local nat_gateways=$(aws ec2 describe-nat-gateways \
        --region "$region" \
        --filter "Name=vpc-id,Values=$vpc_id" "Name=state,Values=pending,available" \
        --query 'NatGateways[*].NatGatewayId' \
        --output text 2>/dev/null || echo "")

    if [ -n "$nat_gateways" ]; then
        return 1  # VPC is in use
    fi

    # Check for VPC Endpoints
    local vpc_endpoints=$(aws ec2 describe-vpc-endpoints \
        --region "$region" \
        --filters "Name=vpc-id,Values=$vpc_id" \
        --query 'VpcEndpoints[*].VpcEndpointId' \
        --output text 2>/dev/null || echo "")

    if [ -n "$vpc_endpoints" ]; then
        return 1  # VPC is in use
    fi

    # Check for Transit Gateway Attachments
    local tgw_attachments=$(aws ec2 describe-transit-gateway-vpc-attachments \
        --region "$region" \
        --filters "Name=vpc-id,Values=$vpc_id" "Name=state,Values=available,pending" \
        --query 'TransitGatewayVpcAttachments[*].TransitGatewayAttachmentId' \
        --output text 2>/dev/null || echo "")

    if [ -n "$tgw_attachments" ]; then
        return 1  # VPC is in use
    fi

    # Check for VPC Peering Connections
    local vpc_peering=$(aws ec2 describe-vpc-peering-connections \
        --region "$region" \
        --filters "Name=requester-vpc-info.vpc-id,Values=$vpc_id" "Name=accepter-vpc-info.vpc-id,Values=$vpc_id" "Name=status-code,Values=active,pending-acceptance" \
        --query 'VpcPeeringConnections[*].VpcPeeringConnectionId' \
        --output text 2>/dev/null || echo "")

    if [ -n "$vpc_peering" ]; then
        return 1  # VPC is in use
    fi

    return 0  # VPC appears unused
}

# Function to delete default VPC and its dependencies
delete_default_vpc() {
    local account_id=$1
    local region=$2
    local vpc_id=$3

    echo -e "${YELLOW}Deleting default VPC $vpc_id in account $account_id, region $region${NC}"

    # Delete internet gateway
    local igw_id=$(aws ec2 describe-internet-gateways \
        --region "$region" \
        --filters "Name=attachment.vpc-id,Values=$vpc_id" \
        --query 'InternetGateways[0].InternetGatewayId' \
        --output text 2>/dev/null || echo "None")

    if [ "$igw_id" != "None" ] && [ -n "$igw_id" ]; then
        echo "  Detaching and deleting Internet Gateway: $igw_id"
        aws ec2 detach-internet-gateway --region "$region" --internet-gateway-id "$igw_id" --vpc-id "$vpc_id" 2>/dev/null || true
        aws ec2 delete-internet-gateway --region "$region" --internet-gateway-id "$igw_id" 2>/dev/null || true
    fi

    # Delete subnets
    local subnet_ids=$(aws ec2 describe-subnets \
        --region "$region" \
        --filters "Name=vpc-id,Values=$vpc_id" \
        --query 'Subnets[*].SubnetId' \
        --output text 2>/dev/null || echo "")

    for subnet_id in $subnet_ids; do
        if [ -n "$subnet_id" ]; then
            echo "  Deleting subnet: $subnet_id"
            aws ec2 delete-subnet --region "$region" --subnet-id "$subnet_id" 2>/dev/null || true
        fi
    done

    # Delete security groups (except default)
    local sg_ids=$(aws ec2 describe-security-groups \
        --region "$region" \
        --filters "Name=vpc-id,Values=$vpc_id" \
        --query 'SecurityGroups[?GroupName!=`default`].GroupId' \
        --output text 2>/dev/null || echo "")

    for sg_id in $sg_ids; do
        if [ -n "$sg_id" ]; then
            echo "  Deleting security group: $sg_id"
            aws ec2 delete-security-group --region "$region" --group-id "$sg_id" 2>/dev/null || true
        fi
    done

    # Delete route tables (except main)
    local rt_ids=$(aws ec2 describe-route-tables \
        --region "$region" \
        --filters "Name=vpc-id,Values=$vpc_id" \
        --query 'RouteTables[?Associations[0].Main!=`true`].RouteTableId' \
        --output text 2>/dev/null || echo "")

    for rt_id in $rt_ids; do
        if [ -n "$rt_id" ]; then
            echo "  Deleting route table: $rt_id"
            aws ec2 delete-route-table --region "$region" --route-table-id "$rt_id" 2>/dev/null || true
        fi
    done

    # Finally delete the VPC
    echo "  Deleting VPC: $vpc_id"
    if aws ec2 delete-vpc --region "$region" --vpc-id "$vpc_id" 2>/dev/null; then
        echo -e "${GREEN}  ✅ Successfully deleted VPC $vpc_id${NC}"
        return 0
    else
        echo -e "${RED}  ❌ Failed to delete VPC $vpc_id${NC}"
        return 1
    fi
}

# Main function to process an account
process_account() {
    local account_id=$1
    local dry_run=$2
    local management_account_id=$3

    log ""
    log "Processing account: $account_id"
    TOTAL_ACCOUNTS=$((TOTAL_ACCOUNTS + 1))

    # Skip management account (no need to assume role)
    if [ "$account_id" = "$management_account_id" ]; then
        log "  (Management account - using current credentials)"
        process_account_regions "$account_id" "$dry_run"
        SUCCESSFUL_ACCOUNTS=$((SUCCESSFUL_ACCOUNTS + 1))
    else
        log "  Assuming cross-account role..."
        if assume_role "$account_id"; then
            log "  ✅ Successfully assumed role"
            process_account_regions "$account_id" "$dry_run"
            reset_credentials
            SUCCESSFUL_ACCOUNTS=$((SUCCESSFUL_ACCOUNTS + 1))
        else
            log "  ❌ Failed to assume role - skipping account"
            FAILED_ACCOUNTS=$((FAILED_ACCOUNTS + 1))
            log_summary "$account_id,ALL,N/A,ROLE_FAILED,SKIPPED,Could not assume cross-account role"
            return 1
        fi
    fi
}

# Function to process regions for an account (with credentials already set)
process_account_regions() {
    local account_id=$1
    local dry_run=$2

    for region in "${ENABLED_REGIONS[@]}"; do
        log "  Checking region: $region"

        # Find default VPC
        local default_vpc=$(aws ec2 describe-vpcs \
            --region "$region" \
            --filters "Name=is-default,Values=true" \
            --query 'Vpcs[0].VpcId' \
            --output text 2>/dev/null || echo "None")

        if [ "$default_vpc" = "None" ] || [ -z "$default_vpc" ]; then
            log "    No default VPC found"
            log_summary "$account_id,$region,N/A,NO_VPC,SKIPPED,No default VPC exists"
            continue
        fi

        log "    Found default VPC: $default_vpc"
        TOTAL_VPCS_FOUND=$((TOTAL_VPCS_FOUND + 1))

        # Check if VPC is unused
        if is_vpc_unused "$account_id" "$region" "$default_vpc"; then
            log "    VPC appears unused"
            TOTAL_VPCS_UNUSED=$((TOTAL_VPCS_UNUSED + 1))

            if [ "$dry_run" = "true" ]; then
                log "    [DRY RUN] Would delete VPC $default_vpc"
                log_summary "$account_id,$region,$default_vpc,UNUSED,DRY_RUN,Would be deleted"
            else
                log "    Deleting VPC $default_vpc"
                if delete_default_vpc "$account_id" "$region" "$default_vpc"; then
                    TOTAL_VPCS_DELETED=$((TOTAL_VPCS_DELETED + 1))
                    log_summary "$account_id,$region,$default_vpc,UNUSED,DELETED,Successfully deleted"
                else
                    log_summary "$account_id,$region,$default_vpc,UNUSED,FAILED,Deletion failed"
                fi
            fi
        else
            log "    VPC is in use - skipping"
            TOTAL_VPCS_IN_USE=$((TOTAL_VPCS_IN_USE + 1))
            log_summary "$account_id,$region,$default_vpc,IN_USE,SKIPPED,VPC has resources"
        fi
    done
}

# Output file setup - create reports directory
REPORTS_DIR="vpc-cleanup-reports"
mkdir -p "$REPORTS_DIR"

# Generate timestamp for this run
TIMESTAMP=$(date +%Y%m%d-%H%M%S)

# Define output files in the reports directory
OUTPUT_FILE="$REPORTS_DIR/vpc-cleanup-report-$TIMESTAMP.txt"
SUMMARY_FILE="$REPORTS_DIR/vpc-cleanup-summary-$TIMESTAMP.csv"

# Function to log to both console and file
log() {
    echo "$1" | tee -a "$OUTPUT_FILE"
}

# Function to log summary data
log_summary() {
    echo "$1" >> "$SUMMARY_FILE"
}

# Main execution
log "=========================================="
log "Default VPC Cleanup Tool"
log "=========================================="
log "Reports directory: $REPORTS_DIR/"
log "Report file: $OUTPUT_FILE"
log "Summary file: $SUMMARY_FILE"
log "Started at: $(date)"
log ""

# Check for command line arguments
TARGET_ACCOUNT=""
if [ $# -eq 1 ]; then
    TARGET_ACCOUNT="$1"
    log "Target account specified: $TARGET_ACCOUNT"

    # Validate account ID format
    if [[ ! "$TARGET_ACCOUNT" =~ ^[0-9]{12}$ ]]; then
        log "❌ Invalid account ID format: $TARGET_ACCOUNT"
        log "Account ID must be exactly 12 digits"
        exit 1
    fi
    log ""
elif [ $# -gt 1 ]; then
    log "❌ Too many arguments provided"
    log "Usage: $0 [account-id]"
    log "  account-id: Optional 12-digit AWS account ID to target"
    log "  If not provided, will process all accounts"
    exit 1
fi

# Get mode from user
echo "Select mode:"
echo "1. Dry run (scan only, no deletions)"
echo "2. Delete unused default VPCs"
if [ -n "$TARGET_ACCOUNT" ]; then
    echo ""
    echo "🎯 Target: Single account ($TARGET_ACCOUNT)"
else
    echo ""
    echo "🌐 Target: All accounts in organization"
fi
read -p "Enter choice (1 or 2): " mode

case $mode in
    1)
        DRY_RUN=true
        log "Running in DRY RUN mode - no resources will be deleted"
        ;;
    2)
        DRY_RUN=false
        log "Running in DELETE mode - unused default VPCs will be deleted"
        read -p "Are you sure? Type 'DELETE' to confirm: " confirm
        if [ "$confirm" != "DELETE" ]; then
            log "Operation cancelled."
            exit 0
        fi
        ;;
    *)
        log "Invalid choice. Exiting."
        exit 1
        ;;
esac

# Initialize summary file with headers
log_summary "Account,Region,VPC_ID,Status,Action,Reason"

# Initialize counters
TOTAL_ACCOUNTS=0
SUCCESSFUL_ACCOUNTS=0
FAILED_ACCOUNTS=0
TOTAL_VPCS_FOUND=0
TOTAL_VPCS_UNUSED=0
TOTAL_VPCS_IN_USE=0
TOTAL_VPCS_DELETED=0

log ""
log "Getting current account and organization info..."

# Store original credentials before we start assuming roles
store_original_credentials

# Get management account ID
MANAGEMENT_ACCOUNT_ID=$(aws sts get-caller-identity --query 'Account' --output text)
log "Management Account ID: $MANAGEMENT_ACCOUNT_ID"

# Get accounts to process
if [ -n "$TARGET_ACCOUNT" ]; then
    log "Processing single target account: $TARGET_ACCOUNT"

    # Verify the target account exists in the organization
    ALL_ACCOUNTS=$(get_all_accounts)
    if [ -z "$ALL_ACCOUNTS" ]; then
        log "❌ Failed to retrieve accounts from AWS Organizations"
        exit 1
    fi

    if echo "$ALL_ACCOUNTS" | grep -q "$TARGET_ACCOUNT"; then
        ACCOUNTS="$TARGET_ACCOUNT"
        ACCOUNT_COUNT=1
        log "✅ Target account found in organization"
    else
        log "❌ Target account $TARGET_ACCOUNT not found in organization"
        log "Available accounts: $(echo "$ALL_ACCOUNTS" | tr ' ' '\n' | head -5 | tr '\n' ' ')..."
        exit 1
    fi
else
    log "Retrieving all accounts from AWS Organizations..."
    ACCOUNTS=$(get_all_accounts)

    if [ -z "$ACCOUNTS" ]; then
        log "❌ Failed to retrieve accounts from AWS Organizations"
        exit 1
    fi

    ACCOUNT_COUNT=$(echo "$ACCOUNTS" | wc -w)
    log "Found $ACCOUNT_COUNT accounts"

    # Debug: Show first few accounts
    log "First 5 accounts: $(echo "$ACCOUNTS" | tr ' ' '\n' | head -5 | tr '\n' ' ')"
fi

log "Will check ${#ENABLED_REGIONS[@]} regions per account"
log ""

# Check if jq is available (needed for parsing assume-role response)
if ! command -v jq &> /dev/null; then
    echo -e "${RED}❌ jq is required but not installed. Please install jq first.${NC}"
    echo "  On macOS: brew install jq"
    echo "  On Ubuntu/Debian: sudo apt-get install jq"
    echo "  On Amazon Linux: sudo yum install jq"
    exit 1
fi

# Process each account
for account in $ACCOUNTS; do
    # Skip empty account IDs
    if [ -z "$account" ] || [ "$account" = "None" ]; then
        continue
    fi

    # Validate account ID format (12 digits)
    if [[ ! "$account" =~ ^[0-9]{12}$ ]]; then
        log "⚠️  Skipping invalid account ID: '$account'"
        continue
    fi

    # Process the account (don't exit on failure)
    process_account "$account" "$DRY_RUN" "$MANAGEMENT_ACCOUNT_ID" || {
        log "⚠️  Account $account processing failed, continuing with next account..."
    }
done

# Generate final summary
log ""
log "=========================================="
log "FINAL SUMMARY"
log "=========================================="
log "Completed at: $(date)"
log ""
log "ACCOUNT STATISTICS:"
log "  Total accounts processed: $TOTAL_ACCOUNTS"
log "  Successful accounts: $SUCCESSFUL_ACCOUNTS"
log "  Failed accounts: $FAILED_ACCOUNTS"
log ""
log "VPC STATISTICS:"
log "  Total default VPCs found: $TOTAL_VPCS_FOUND"
log "  Unused VPCs (candidates for deletion): $TOTAL_VPCS_UNUSED"
log "  VPCs in use (skipped): $TOTAL_VPCS_IN_USE"
if [ "$DRY_RUN" = "false" ]; then
    log "  VPCs successfully deleted: $TOTAL_VPCS_DELETED"
fi
log ""
log "FILES GENERATED:"
log "  Detailed report: $OUTPUT_FILE"
log "  CSV summary: $SUMMARY_FILE"
log ""

# Also create a quick stats file
STATS_FILE="$REPORTS_DIR/vpc-cleanup-stats-$TIMESTAMP.txt"
cat > "$STATS_FILE" << EOF
VPC Cleanup Statistics - $(date)
=====================================

Accounts:
- Total: $TOTAL_ACCOUNTS
- Successful: $SUCCESSFUL_ACCOUNTS
- Failed: $FAILED_ACCOUNTS

Default VPCs:
- Found: $TOTAL_VPCS_FOUND
- Unused: $TOTAL_VPCS_UNUSED
- In Use: $TOTAL_VPCS_IN_USE
$(if [ "$DRY_RUN" = "false" ]; then echo "- Deleted: $TOTAL_VPCS_DELETED"; fi)

Mode: $(if [ "$DRY_RUN" = "true" ]; then echo "DRY RUN"; else echo "DELETE"; fi)

Files:
- Report: $OUTPUT_FILE
- Summary: $SUMMARY_FILE
- Stats: $STATS_FILE
EOF

echo ""
echo "=========================================="
echo "SCAN COMPLETE!"
echo "=========================================="
echo "📊 Quick Stats:"
echo "   Accounts processed: $SUCCESSFUL_ACCOUNTS/$TOTAL_ACCOUNTS"
echo "   Default VPCs found: $TOTAL_VPCS_FOUND"
echo "   Unused VPCs: $TOTAL_VPCS_UNUSED"
if [ "$DRY_RUN" = "false" ]; then
    echo "   VPCs deleted: $TOTAL_VPCS_DELETED"
fi
echo ""
echo "📁 Output files created:"
echo "   📄 Detailed report: $OUTPUT_FILE"
echo "   📊 CSV summary: $SUMMARY_FILE"
echo "   📈 Quick stats: $STATS_FILE"
echo ""
echo "💡 To analyze results:"
echo "   cat $STATS_FILE"
echo "   head -20 $OUTPUT_FILE"
echo "   open $SUMMARY_FILE  # (in Excel/Numbers)"
