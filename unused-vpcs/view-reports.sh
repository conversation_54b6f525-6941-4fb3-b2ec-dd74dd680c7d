#!/bin/bash

# Helper script to view and manage VPC cleanup reports
# This helps you navigate the compliance evidence files

REPORTS_DIR="vpc-cleanup-reports"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo "=========================================="
echo "VPC Cleanup Reports Manager"
echo "=========================================="

# Check if reports directory exists
if [ ! -d "$REPORTS_DIR" ]; then
    echo -e "${RED}❌ Reports directory not found: $REPORTS_DIR${NC}"
    echo "Run the VPC cleanup script first to generate reports."
    exit 1
fi

# Count files
REPORT_COUNT=$(find "$REPORTS_DIR" -name "*.txt" -o -name "*.csv" | wc -l)

if [ "$REPORT_COUNT" -eq 0 ]; then
    echo -e "${YELLOW}⚠️  No reports found in $REPORTS_DIR${NC}"
    exit 0
fi

echo -e "${GREEN}📁 Reports directory: $REPORTS_DIR${NC}"
echo -e "${BLUE}📊 Total files: $REPORT_COUNT${NC}"
echo ""

# Function to show recent reports
show_recent_reports() {
    echo "Recent Reports (last 10):"
    echo "----------------------------------------"
    
    # List files by modification time, most recent first
    find "$REPORTS_DIR" -name "*.txt" -o -name "*.csv" | \
        xargs ls -lt | \
        head -10 | \
        awk '{
            # Extract just the filename from the full path
            split($9, path_parts, "/")
            filename = path_parts[length(path_parts)]
            
            # Color code by file type
            if (filename ~ /stats/) 
                printf "📈 %s %s %s \033[0;36m%s\033[0m\n", $6, $7, $8, filename
            else if (filename ~ /summary.*\.csv/) 
                printf "📊 %s %s %s \033[0;32m%s\033[0m\n", $6, $7, $8, filename
            else if (filename ~ /report.*\.txt/) 
                printf "📄 %s %s %s \033[0;33m%s\033[0m\n", $6, $7, $8, filename
            else 
                printf "📋 %s %s %s %s\n", $6, $7, $8, filename
        }'
    echo ""
}

# Function to show summary of all runs
show_summary() {
    echo "Summary of All Runs:"
    echo "----------------------------------------"
    
    local total_runs=0
    local total_accounts=0
    local total_vpcs_found=0
    local total_vpcs_deleted=0
    
    for stats_file in "$REPORTS_DIR"/vpc-cleanup-stats-*.txt; do
        if [ -f "$stats_file" ]; then
            total_runs=$((total_runs + 1))
            
            # Extract stats from file
            local accounts=$(grep "^- Total:" "$stats_file" | awk '{print $3}' || echo "0")
            local found=$(grep "^- Found:" "$stats_file" | awk '{print $3}' || echo "0")
            local deleted=$(grep "^- Deleted:" "$stats_file" | awk '{print $3}' || echo "0")
            
            total_accounts=$((total_accounts + accounts))
            total_vpcs_found=$((total_vpcs_found + found))
            total_vpcs_deleted=$((total_vpcs_deleted + deleted))
            
            # Show individual run
            local timestamp=$(basename "$stats_file" | sed 's/vpc-cleanup-stats-\(.*\)\.txt/\1/')
            echo "  Run $timestamp: $accounts accounts, $found VPCs found, $deleted deleted"
        fi
    done
    
    echo ""
    echo "Overall Totals:"
    echo "  Total runs: $total_runs"
    echo "  Total accounts processed: $total_accounts"
    echo "  Total VPCs found: $total_vpcs_found"
    echo "  Total VPCs deleted: $total_vpcs_deleted"
    echo ""
}

# Function to open latest report
open_latest() {
    local latest_report=$(find "$REPORTS_DIR" -name "vpc-cleanup-report-*.txt" | xargs ls -t | head -1)
    local latest_summary=$(find "$REPORTS_DIR" -name "vpc-cleanup-summary-*.csv" | xargs ls -t | head -1)
    local latest_stats=$(find "$REPORTS_DIR" -name "vpc-cleanup-stats-*.txt" | xargs ls -t | head -1)
    
    if [ -f "$latest_stats" ]; then
        echo "Latest Stats:"
        echo "----------------------------------------"
        cat "$latest_stats"
        echo ""
    fi
    
    if [ -f "$latest_summary" ]; then
        echo "Opening latest CSV summary: $latest_summary"
        if command -v open >/dev/null 2>&1; then
            open "$latest_summary"
        elif command -v xdg-open >/dev/null 2>&1; then
            xdg-open "$latest_summary"
        else
            echo "CSV file: $latest_summary"
        fi
    fi
    
    if [ -f "$latest_report" ]; then
        echo ""
        echo "Latest detailed report: $latest_report"
        echo "Use: head -50 '$latest_report' to view the beginning"
        echo "Use: tail -50 '$latest_report' to view the end"
    fi
}

# Main menu
if [ $# -eq 0 ]; then
    show_recent_reports
    show_summary
    
    echo "Commands:"
    echo "  $0 list     - Show recent reports"
    echo "  $0 summary  - Show summary of all runs"
    echo "  $0 latest   - Open/view latest reports"
    echo "  $0 clean    - Clean up old reports (interactive)"
    echo ""
    echo "💡 Tip: All files in $REPORTS_DIR/ are compliance evidence"
    echo "   Keep them for audit purposes!"
    
elif [ "$1" = "list" ]; then
    show_recent_reports
    
elif [ "$1" = "summary" ]; then
    show_summary
    
elif [ "$1" = "latest" ]; then
    open_latest
    
elif [ "$1" = "clean" ]; then
    echo "Clean up old reports:"
    echo "----------------------------------------"
    echo "⚠️  This will permanently delete old report files!"
    echo ""
    echo "Reports older than how many days should be deleted?"
    read -p "Enter number of days (or 'cancel' to abort): " days
    
    if [ "$days" = "cancel" ]; then
        echo "Cleanup cancelled."
        exit 0
    fi
    
    if [[ "$days" =~ ^[0-9]+$ ]] && [ "$days" -gt 0 ]; then
        echo ""
        echo "Files that would be deleted (older than $days days):"
        find "$REPORTS_DIR" -name "*.txt" -o -name "*.csv" | xargs ls -lt | \
            awk -v days="$days" 'BEGIN{cutoff=systime()-days*86400} {
                cmd="date -r " $9 " +%s"
                cmd | getline file_time
                close(cmd)
                if(file_time < cutoff) print $9
            }'
        
        echo ""
        read -p "Proceed with deletion? (y/N): " confirm
        if [[ "$confirm" =~ ^[Yy]$ ]]; then
            find "$REPORTS_DIR" -name "*.txt" -o -name "*.csv" | xargs ls -lt | \
                awk -v days="$days" 'BEGIN{cutoff=systime()-days*86400} {
                    cmd="date -r " $9 " +%s"
                    cmd | getline file_time
                    close(cmd)
                    if(file_time < cutoff) system("rm " $9)
                }'
            echo "✅ Cleanup complete"
        else
            echo "Cleanup cancelled"
        fi
    else
        echo "❌ Invalid input. Please enter a positive number."
    fi
    
else
    echo "❌ Unknown command: $1"
    echo "Use: $0 (without arguments) to see available commands"
fi
