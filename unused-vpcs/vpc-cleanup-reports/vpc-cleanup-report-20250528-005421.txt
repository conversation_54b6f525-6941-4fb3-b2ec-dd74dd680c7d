==========================================
Default VPC Cleanup Tool
==========================================
Reports directory: vpc-cleanup-reports/
Report file: vpc-cleanup-reports/vpc-cleanup-report-********-005421.txt
Summary file: vpc-cleanup-reports/vpc-cleanup-summary-********-005421.csv
Started at: Wed May 28 00:54:21 CST 2025

Target account specified: ************

Running in DRY RUN mode - no resources will be deleted

Getting current account and organization info...
Management Account ID: ************
Processing single target account: ************
✅ Target account found in organization
Will check 18 regions per account


Processing account: ************
  Assuming cross-account role...
  ✅ Successfully assumed role
  Checking region: ap-south-1
    Found default VPC: vpc-012f76b2df493ac80
    VPC appears unused
    [DRY RUN] Would delete VPC vpc-012f76b2df493ac80
  Checking region: eu-north-1
    Found default VPC: vpc-0e8aca738599bc20c
    VPC appears unused
    [DRY RUN] Would delete VPC vpc-0e8aca738599bc20c
  Checking region: eu-west-3
    Found default VPC: vpc-035f7c390df1e3af0
    VPC appears unused
    [DRY RUN] Would delete VPC vpc-035f7c390df1e3af0
  Checking region: eu-west-2
    Found default VPC: vpc-0e3b83d8253c4ea83
    VPC appears unused
    [DRY RUN] Would delete VPC vpc-0e3b83d8253c4ea83
  Checking region: eu-west-1
    Found default VPC: vpc-0d255f196527d39f4
    VPC appears unused
    [DRY RUN] Would delete VPC vpc-0d255f196527d39f4
  Checking region: ap-northeast-3
    Found default VPC: vpc-0044225c69d6ceec5
    VPC appears unused
    [DRY RUN] Would delete VPC vpc-0044225c69d6ceec5
  Checking region: ap-northeast-2
    Found default VPC: vpc-07f5b6a7527898366
    VPC appears unused
    [DRY RUN] Would delete VPC vpc-07f5b6a7527898366
  Checking region: ap-northeast-1
    Found default VPC: vpc-00d1342592757af6c
    VPC appears unused
    [DRY RUN] Would delete VPC vpc-00d1342592757af6c
  Checking region: ca-central-1
    No default VPC found
  Checking region: sa-east-1
    Found default VPC: vpc-07e64f58616ba2005
    VPC appears unused
    [DRY RUN] Would delete VPC vpc-07e64f58616ba2005
  Checking region: ap-southeast-1
    Found default VPC: vpc-008e4f512f71ff353
    VPC appears unused
    [DRY RUN] Would delete VPC vpc-008e4f512f71ff353
  Checking region: ap-southeast-2
    Found default VPC: vpc-0b3220567847ee100
    VPC appears unused
    [DRY RUN] Would delete VPC vpc-0b3220567847ee100
  Checking region: eu-central-1
    Found default VPC: vpc-035ae2ea03db8a7de
    VPC appears unused
    [DRY RUN] Would delete VPC vpc-035ae2ea03db8a7de
  Checking region: ap-southeast-3
    No default VPC found
  Checking region: us-east-1
    No default VPC found
  Checking region: us-east-2
    Found default VPC: vpc-0b7c8bd37bbe3505f
    VPC appears unused
    [DRY RUN] Would delete VPC vpc-0b7c8bd37bbe3505f
  Checking region: us-west-1
    Found default VPC: vpc-0bfec72cffab661e2
    VPC appears unused
    [DRY RUN] Would delete VPC vpc-0bfec72cffab661e2
  Checking region: us-west-2
    Found default VPC: vpc-07b62c603e26ced9b
    VPC appears unused
    [DRY RUN] Would delete VPC vpc-07b62c603e26ced9b

==========================================
FINAL SUMMARY
==========================================
Completed at: Wed May 28 00:57:52 CST 2025

ACCOUNT STATISTICS:
  Total accounts processed: 1
  Successful accounts: 1
  Failed accounts: 0

VPC STATISTICS:
  Total default VPCs found: 15
  Unused VPCs (candidates for deletion): 15
  VPCs in use (skipped): 0

FILES GENERATED:
  Detailed report: vpc-cleanup-reports/vpc-cleanup-report-********-005421.txt
  CSV summary: vpc-cleanup-reports/vpc-cleanup-summary-********-005421.csv

