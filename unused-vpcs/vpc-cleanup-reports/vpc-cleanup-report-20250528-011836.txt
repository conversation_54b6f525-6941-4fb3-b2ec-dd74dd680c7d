==========================================
Default VPC Cleanup Tool
==========================================
Reports directory: vpc-cleanup-reports/
Report file: vpc-cleanup-reports/vpc-cleanup-report-********-011836.txt
Summary file: vpc-cleanup-reports/vpc-cleanup-summary-********-011836.csv
Started at: Wed May 28 01:18:36 CST 2025

Target account specified: ************

Running in DRY RUN mode - no resources will be deleted

Getting current account and organization info...
Management Account ID: ************
Processing single target account: ************
✅ Target account found in organization
Will check 18 regions per account


Processing account: ************
  Assuming cross-account role...
  ✅ Successfully assumed role
  Checking region: ap-south-1
    Found default VPC: vpc-70ed2919
    VPC appears unused
    [DRY RUN] Would delete VPC vpc-70ed2919
  Checking region: eu-north-1
    Found default VPC: vpc-3a6a8753
    VPC appears unused
    [DRY RUN] Would delete VPC vpc-3a6a8753
  Checking region: eu-west-3
    Found default VPC: vpc-492bea20
    VPC appears unused
    [DRY RUN] Would delete VPC vpc-492bea20
  Checking region: eu-west-2
    Found default VPC: vpc-e914f680
    VPC appears unused
    [DRY RUN] Would delete VPC vpc-e914f680
  Checking region: eu-west-1
    Found default VPC: vpc-98d11dfc
    VPC appears unused
    [DRY RUN] Would delete VPC vpc-98d11dfc
  Checking region: ap-northeast-3
    Found default VPC: vpc-18d23e71
    VPC appears unused
    [DRY RUN] Would delete VPC vpc-18d23e71
  Checking region: ap-northeast-2
    Found default VPC: vpc-66cf610f
    VPC appears unused
    [DRY RUN] Would delete VPC vpc-66cf610f
  Checking region: ap-northeast-1
    Found default VPC: vpc-d87640bd
    VPC appears unused
    [DRY RUN] Would delete VPC vpc-d87640bd
  Checking region: ca-central-1
    Found default VPC: vpc-4d3dd424
    VPC appears unused
    [DRY RUN] Would delete VPC vpc-4d3dd424
  Checking region: sa-east-1
    Found default VPC: vpc-9e698cfa
    VPC appears unused
    [DRY RUN] Would delete VPC vpc-9e698cfa
  Checking region: ap-southeast-1
    Found default VPC: vpc-5adede3f
    VPC appears unused
    [DRY RUN] Would delete VPC vpc-5adede3f
  Checking region: ap-southeast-2
    Found default VPC: vpc-7a73541f
    VPC appears unused
    [DRY RUN] Would delete VPC vpc-7a73541f
  Checking region: eu-central-1
    Found default VPC: vpc-b70d24de
    VPC appears unused
    [DRY RUN] Would delete VPC vpc-b70d24de
  Checking region: ap-southeast-3
    Found default VPC: vpc-011cd2e58eef7599f
    VPC appears unused
    [DRY RUN] Would delete VPC vpc-011cd2e58eef7599f
  Checking region: us-east-1
    Found default VPC: vpc-8faa70e8
    VPC is in use - skipping
  Checking region: us-east-2
    Found default VPC: vpc-20ca3049
    VPC is in use - skipping
  Checking region: us-west-1
    Found default VPC: vpc-658cd500
    VPC appears unused
    [DRY RUN] Would delete VPC vpc-658cd500
  Checking region: us-west-2
    Found default VPC: vpc-f87c5c9d
    VPC is in use - skipping

==========================================
FINAL SUMMARY
==========================================
Completed at: Wed May 28 01:22:19 CST 2025

ACCOUNT STATISTICS:
  Total accounts processed: 1
  Successful accounts: 1
  Failed accounts: 0

VPC STATISTICS:
  Total default VPCs found: 18
  Unused VPCs (candidates for deletion): 15
  VPCs in use (skipped): 3

FILES GENERATED:
  Detailed report: vpc-cleanup-reports/vpc-cleanup-report-********-011836.txt
  CSV summary: vpc-cleanup-reports/vpc-cleanup-summary-********-011836.csv

