#!/bin/bash

# Helper script to test VPC cleanup on a single account
# Usage: ./test-single-account.sh <account-id>

if [ $# -ne 1 ]; then
    echo "Usage: $0 <account-id>"
    echo ""
    echo "Examples:"
    echo "  $0 ************    # Test on specific account"
    echo ""
    echo "This script will run the VPC cleanup on a single account for testing."
    exit 1
fi

ACCOUNT_ID="$1"

# Validate account ID format
if [[ ! "$ACCOUNT_ID" =~ ^[0-9]{12}$ ]]; then
    echo "❌ Invalid account ID format: $ACCOUNT_ID"
    echo "Account ID must be exactly 12 digits"
    exit 1
fi

echo "=========================================="
echo "Single Account VPC Cleanup Test"
echo "=========================================="
echo "Target Account: $ACCOUNT_ID"
echo ""

# Check if the main script exists
if [ ! -f "./find-delete-default-vpcs.sh" ]; then
    echo "❌ Main script not found: ./find-delete-default-vpcs.sh"
    echo "Make sure you're running this from the correct directory."
    exit 1
fi

# Make sure the main script is executable
chmod +x ./find-delete-default-vpcs.sh

echo "🚀 Starting VPC cleanup for account $ACCOUNT_ID..."
echo ""

# Run the main script with the account ID
./find-delete-default-vpcs.sh "$ACCOUNT_ID"

echo ""
echo "=========================================="
echo "Single Account Test Complete"
echo "=========================================="
