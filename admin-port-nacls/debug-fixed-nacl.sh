#!/opt/homebrew/bin/bash

# Debug script to check if the DENY rules were actually added
# NACL: acl-0e2be52998303ac58 in account ************, region us-east-1

ACCOUNT_ID="************"
REGION="us-east-1"
NACL_ID="acl-0e2be52998303ac58"

echo "=========================================="
echo "Debugging Fixed NACL"
echo "=========================================="
echo "Account: $ACCOUNT_ID"
echo "Region: $REGION"
echo "NACL: $NACL_ID"
echo ""

# Get management account ID
MANAGEMENT_ACCOUNT_ID=$(aws sts get-caller-identity --query 'Account' --output text)
echo "Management Account: $MANAGEMENT_ACCOUNT_ID"

# Assume cross-account role
echo ""
echo "🔑 Assuming cross-account role..."
ROLE_ARN="arn:aws:iam::${ACCOUNT_ID}:role/CrossAccountVPCManagementRole"
EXTERNAL_ID="DrataCleanup2025"

ASSUME_RESULT=$(aws sts assume-role \
    --role-arn "$ROLE_ARN" \
    --role-session-name "NACLDebug-$(date +%s)" \
    --external-id "$EXTERNAL_ID" \
    --duration-seconds 900 2>&1)

if [ $? -ne 0 ]; then
    echo "❌ Failed to assume role: $ASSUME_RESULT"
    exit 1
fi

export AWS_ACCESS_KEY_ID=$(echo "$ASSUME_RESULT" | jq -r '.Credentials.AccessKeyId')
export AWS_SECRET_ACCESS_KEY=$(echo "$ASSUME_RESULT" | jq -r '.Credentials.SecretAccessKey')
export AWS_SESSION_TOKEN=$(echo "$ASSUME_RESULT" | jq -r '.Credentials.SessionToken')

echo "✅ Successfully assumed role"

# Get NACL details
echo ""
echo "📋 Fetching current NACL state..."
NACL_DETAILS=$(aws ec2 describe-network-acls \
    --region "$REGION" \
    --network-acl-ids "$NACL_ID" \
    --output json 2>&1)

if [ $? -ne 0 ]; then
    echo "❌ Failed to get NACL details: $NACL_DETAILS"
    exit 1
fi

# Check if NACL exists
NACL_COUNT=$(echo "$NACL_DETAILS" | jq '.NetworkAcls | length')
if [ "$NACL_COUNT" -eq 0 ]; then
    echo "❌ NACL not found"
    exit 1
fi

echo "✅ NACL found"

# Extract basic info
IS_DEFAULT=$(echo "$NACL_DETAILS" | jq -r '.NetworkAcls[0].IsDefault')
VPC_ID=$(echo "$NACL_DETAILS" | jq -r '.NetworkAcls[0].VpcId')
ENTRIES=$(echo "$NACL_DETAILS" | jq '.NetworkAcls[0].Entries')

echo ""
echo "📊 NACL Information:"
echo "  VPC ID: $VPC_ID"
echo "  Is Default: $IS_DEFAULT"
echo ""

# Show all entries in detail
echo "📋 Current NACL Entries (Raw API Response):"
echo "$ENTRIES" | jq '.'
echo ""

# Show entries in human-readable format
echo "📋 NACL Entries (Human Readable):"
echo "Rule# | Egress | Action | Protocol | CIDR        | Port Range"
echo "------|--------|--------|----------|-------------|------------"
echo "$ENTRIES" | jq -r '.[] | "\(.RuleNumber | tostring | . + (6 - length) * " ") | \(.Egress | tostring | . + (6 - length) * " ") | \(.RuleAction | . + (6 - length) * " ") | \(.Protocol | . + (8 - length) * " ") | \(.CidrBlock // .Ipv6CidrBlock // "N/A" | . + (11 - length) * " ") | \(.PortRange.From // "ALL")-\(.PortRange.To // "ALL")"'
echo ""

# Check for the DENY rules we should have added
echo "🔍 Checking for DENY rules that should have been added:"
echo ""

# Look for SSH DENY rules
echo "SSH DENY rules (should be rules 90 and 92):"
SSH_DENY_RULES=$(echo "$ENTRIES" | jq -r '
    .[] | select(
        .Egress == false and
        .RuleAction == "deny" and 
        .CidrBlock == "0.0.0.0/0" and
        (.PortRange.From == 22 and .PortRange.To == 22)
    ) | "  Rule \(.RuleNumber): \(.Protocol) port \(.PortRange.From)-\(.PortRange.To) DENY"'
)

if [ -n "$SSH_DENY_RULES" ]; then
    echo "$SSH_DENY_RULES"
else
    echo "  ❌ No SSH DENY rules found"
fi

echo ""

# Look for RDP DENY rules
echo "RDP DENY rules (should be rules 91 and 93):"
RDP_DENY_RULES=$(echo "$ENTRIES" | jq -r '
    .[] | select(
        .Egress == false and
        .RuleAction == "deny" and 
        .CidrBlock == "0.0.0.0/0" and
        (.PortRange.From == 3389 and .PortRange.To == 3389)
    ) | "  Rule \(.RuleNumber): \(.Protocol) port \(.PortRange.From)-\(.PortRange.To) DENY"'
)

if [ -n "$RDP_DENY_RULES" ]; then
    echo "$RDP_DENY_RULES"
else
    echo "  ❌ No RDP DENY rules found"
fi

echo ""

# Check for any DENY rules at all
echo "All DENY rules in this NACL:"
ALL_DENY_RULES=$(echo "$ENTRIES" | jq -r '
    .[] | select(.RuleAction == "deny") | 
    "  Rule \(.RuleNumber): \(.RuleAction) \(.Protocol) \(.CidrBlock // "N/A") ports \(.PortRange.From // "ALL")-\(.PortRange.To // "ALL") egress:\(.Egress)"'
)

if [ -n "$ALL_DENY_RULES" ]; then
    echo "$ALL_DENY_RULES"
else
    echo "  ❌ No DENY rules found at all"
fi

echo ""

# Summary
echo "=========================================="
echo "Analysis Summary"
echo "=========================================="

TOTAL_RULES=$(echo "$ENTRIES" | jq length)
DENY_RULES_COUNT=$(echo "$ENTRIES" | jq '[.[] | select(.RuleAction == "deny")] | length')
SSH_DENY_COUNT=$(echo "$ENTRIES" | jq '[.[] | select(.Egress == false and .RuleAction == "deny" and .CidrBlock == "0.0.0.0/0" and (.PortRange.From == 22 and .PortRange.To == 22))] | length')
RDP_DENY_COUNT=$(echo "$ENTRIES" | jq '[.[] | select(.Egress == false and .RuleAction == "deny" and .CidrBlock == "0.0.0.0/0" and (.PortRange.From == 3389 and .PortRange.To == 3389))] | length')

echo "Total rules: $TOTAL_RULES"
echo "Total DENY rules: $DENY_RULES_COUNT"
echo "SSH DENY rules: $SSH_DENY_COUNT"
echo "RDP DENY rules: $RDP_DENY_COUNT"
echo ""

if [ "$SSH_DENY_COUNT" -gt 0 ] && [ "$RDP_DENY_COUNT" -gt 0 ]; then
    echo "✅ DENY rules were successfully added!"
    echo "The remediation worked correctly."
elif [ "$DENY_RULES_COUNT" -gt 0 ]; then
    echo "⚠️  Some DENY rules exist, but not the expected SSH/RDP rules"
    echo "Check the rules above to see what was actually added."
else
    echo "❌ No DENY rules found - the remediation did not work"
    echo "The script reported success but no changes were made."
fi

# Reset credentials
unset AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN
