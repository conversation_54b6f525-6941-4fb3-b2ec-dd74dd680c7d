#!/opt/homebrew/bin/bash

# Fix NACL rules that allow administrative traffic (SSH and RDP) from the internet
# This script adds DENY rules for SSH (22) and RDP (3389) from 0.0.0.0/0 while preserving existing rules

# Note: We don't use 'set -e' here because we want to continue processing other accounts
# even if some accounts fail (e.g., suspended accounts, role assumption failures)

# Configuration
ROOT_OU_ID="r-3g6q"
# Allow custom CSV file via environment variable (for Terraform-safe mode)
FAILING_RESOURCES_FILE="${CUSTOM_CSV:-failing-resources.csv}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Create reports directory
REPORTS_DIR="nacl-reports"
mkdir -p "$REPORTS_DIR"

# Generate timestamp for this run
TIMESTAMP=$(date +"%Y%m%d-%H%M%S")
DETAILED_REPORT="$REPORTS_DIR/nacl-remediation-report-$TIMESTAMP.txt"
SUMMARY_REPORT="$REPORTS_DIR/nacl-remediation-summary-$TIMESTAMP.csv"
STATS_REPORT="$REPORTS_DIR/nacl-remediation-stats-$TIMESTAMP.txt"
BACKUP_REPORT="$REPORTS_DIR/nacl-backups-$TIMESTAMP.json"

# Initialize counters
TOTAL_ACCOUNTS_PROCESSED=0
SUCCESSFUL_ACCOUNTS=0
FAILED_ACCOUNTS=0
TOTAL_NACLS_PROCESSED=0
NACLS_FIXED=0
NACLS_BACKED_UP=0
RULES_REMOVED=0

# Function to log messages
log() {
    local message="$1"
    echo -e "$message" | tee -a "$DETAILED_REPORT"
}

# Function to log summary data
log_summary() {
    echo "$1" >> "$SUMMARY_REPORT"
}

# Function to assume cross-account role
assume_role() {
    local account_id=$1
    local role_name="CrossAccountVPCManagementRole"
    local external_id="DrataCleanup2025"

    local role_arn="arn:aws:iam::${account_id}:role/${role_name}"

    # Assume the role and export credentials
    local assume_result=$(aws sts assume-role \
        --role-arn "$role_arn" \
        --role-session-name "NACLRemediation-$(date +%s)" \
        --external-id "$external_id" \
        --duration-seconds 3600 2>/dev/null || echo "ERROR")

    if [ "$assume_result" = "ERROR" ]; then
        return 1
    fi

    # Export the credentials for subsequent AWS CLI calls
    export AWS_ACCESS_KEY_ID=$(echo "$assume_result" | jq -r '.Credentials.AccessKeyId')
    export AWS_SECRET_ACCESS_KEY=$(echo "$assume_result" | jq -r '.Credentials.SecretAccessKey')
    export AWS_SESSION_TOKEN=$(echo "$assume_result" | jq -r '.Credentials.SessionToken')

    return 0
}

# Store original credentials at script start
ORIGINAL_AWS_ACCESS_KEY_ID="$AWS_ACCESS_KEY_ID"
ORIGINAL_AWS_SECRET_ACCESS_KEY="$AWS_SECRET_ACCESS_KEY"
ORIGINAL_AWS_SESSION_TOKEN="$AWS_SESSION_TOKEN"

# Function to reset credentials to original
reset_credentials() {
    if [ -n "$ORIGINAL_AWS_ACCESS_KEY_ID" ]; then
        export AWS_ACCESS_KEY_ID="$ORIGINAL_AWS_ACCESS_KEY_ID"
    else
        unset AWS_ACCESS_KEY_ID
    fi

    if [ -n "$ORIGINAL_AWS_SECRET_ACCESS_KEY" ]; then
        export AWS_SECRET_ACCESS_KEY="$ORIGINAL_AWS_SECRET_ACCESS_KEY"
    else
        unset AWS_SECRET_ACCESS_KEY
    fi

    if [ -n "$ORIGINAL_AWS_SESSION_TOKEN" ]; then
        export AWS_SESSION_TOKEN="$ORIGINAL_AWS_SESSION_TOKEN"
    else
        unset AWS_SESSION_TOKEN
    fi
}

# Function to backup NACL configuration
backup_nacl() {
    local nacl_id=$1
    local region=$2
    local account_id=$3

    log "      Creating backup for NACL $nacl_id..."

    # Get complete NACL configuration
    local nacl_config=$(aws ec2 describe-network-acls \
        --region "$region" \
        --network-acl-ids "$nacl_id" \
        --output json 2>/dev/null)

    if [ $? -eq 0 ] && [ "$nacl_config" != "null" ]; then
        # Add backup to JSON file
        local backup_entry=$(echo "$nacl_config" | jq --arg account "$account_id" --arg timestamp "$(date -u +%Y-%m-%dT%H:%M:%SZ)" \
            '.NetworkAcls[0] | {account: $account, timestamp: $timestamp, backup: .}')

        # Append to backup file (create array if first entry)
        if [ ! -f "$BACKUP_REPORT" ]; then
            echo "[]" > "$BACKUP_REPORT"
        fi

        # Add entry to backup array
        local temp_file=$(mktemp)
        jq ". += [$backup_entry]" "$BACKUP_REPORT" > "$temp_file" && mv "$temp_file" "$BACKUP_REPORT"

        NACLS_BACKED_UP=$((NACLS_BACKED_UP + 1))
        log "        ✅ Backup created successfully"
        return 0
    else
        log "        ❌ Failed to create backup"
        return 1
    fi
}

# Function to remove admin port rules from NACL
fix_nacl_admin_rules() {
    local nacl_id=$1
    local region=$2
    local account_id=$3
    local dry_run=$4

    log "      Analyzing and fixing NACL rules for $nacl_id..."

    # Get NACL entries
    local nacl_entries=$(aws ec2 describe-network-acls \
        --region "$region" \
        --network-acl-ids "$nacl_id" \
        --query 'NetworkAcls[0].Entries' \
        --output json 2>/dev/null || echo "[]")

    if [ "$nacl_entries" = "[]" ]; then
        log "        ❌ Failed to retrieve NACL entries"
        log_summary "$account_id,$region,$nacl_id,ERROR,FAILED,Could not retrieve NACL entries"
        return 1
    fi

    # Check if it's a default NACL
    local is_default=$(aws ec2 describe-network-acls \
        --region "$region" \
        --network-acl-ids "$nacl_id" \
        --query 'NetworkAcls[0].IsDefault' \
        --output text 2>/dev/null || echo "false")

    local nacl_type="CUSTOM"
    if [ "$is_default" = "true" ]; then
        nacl_type="DEFAULT"
    fi

    # Get VPC ID for context
    local vpc_id=$(aws ec2 describe-network-acls \
        --region "$region" \
        --network-acl-ids "$nacl_id" \
        --query 'NetworkAcls[0].VpcId' \
        --output text 2>/dev/null || echo "unknown")

    # Find problematic rules using DRATA exact methodology
    # DRATA: inbound rules with TCP(6), UDP(17), or ALL(-1) protocols allowing 0.0.0.0/0 to admin ports
    local problematic_rules=$(echo "$nacl_entries" | jq -r '
        .[] | select(
            .Egress == false and
            .RuleAction == "allow" and
            .CidrBlock == "0.0.0.0/0" and
            (.Protocol == "6" or .Protocol == "17" or .Protocol == "-1") and
            (
                (.PortRange.From <= 22 and .PortRange.To >= 22) or
                (.PortRange.From <= 3389 and .PortRange.To >= 3389) or
                (.PortRange == null)
            )
        ) | .RuleNumber'
    )

    if [ -z "$problematic_rules" ]; then
        log "        ✅ No problematic admin port rules found"
        log_summary "$account_id,$region,$nacl_id,$nacl_type,$vpc_id,NO_ISSUES,No admin port rules found"
        return 0
    fi

    # Create backup before making changes
    if [ "$dry_run" = "false" ]; then
        if ! backup_nacl "$nacl_id" "$region" "$account_id"; then
            log "        ❌ Backup failed - skipping remediation for safety"
            log_summary "$account_id,$region,$nacl_id,$nacl_type,$vpc_id,BACKUP_FAILED,Could not create backup"
            return 1
        fi
    fi

    local rules_removed_count=0
    local action_taken=""

    # Process each problematic rule
    for rule_number in $problematic_rules; do
        # Get rule details
        local rule_details=$(echo "$nacl_entries" | jq -r --arg rn "$rule_number" '
            .[] | select(.RuleNumber == ($rn | tonumber)) |
            "Protocol:\(.Protocol) Port:\(.PortRange.From)-\(.PortRange.To) Action:\(.RuleAction)"'
        )

        log "        🔍 Found problematic rule $rule_number: $rule_details"

        if [ "$dry_run" = "true" ]; then
            log "        [DRY RUN] Would add DENY rules for SSH and RDP before rule $rule_number"
            action_taken="${action_taken}DRY_RUN_ADD_DENY_${rule_number};"
        else
            log "        🔧 Adding DENY rules for SSH and RDP before rule $rule_number..."

            # Find available rule numbers before the problematic rule
            # Get all existing rule numbers to avoid conflicts
            local existing_rules=$(echo "$nacl_entries" | jq -r '.[] | select(.Egress == false) | .RuleNumber' | sort -n)
            log "        📋 Existing inbound rule numbers: $(echo $existing_rules | tr '\n' ' ')"

            # Find available rule numbers (start from 10 and work up)
            local ssh_tcp_rule=10
            local ssh_udp_rule=11
            local rdp_tcp_rule=12
            local rdp_udp_rule=13

            # Check if these rule numbers are available, if not find alternatives
            for rule_num in 10 11 12 13 14 15 16 17 18 19 20; do
                if ! echo "$existing_rules" | grep -q "^$rule_num$"; then
                    if [ $ssh_tcp_rule -eq 10 ]; then
                        ssh_tcp_rule=$rule_num
                    elif [ $ssh_udp_rule -eq 11 ]; then
                        ssh_udp_rule=$rule_num
                    elif [ $rdp_tcp_rule -eq 12 ]; then
                        rdp_tcp_rule=$rule_num
                    elif [ $rdp_udp_rule -eq 13 ]; then
                        rdp_udp_rule=$rule_num
                        break
                    fi
                fi
            done

            log "        📋 Will use rule numbers: SSH-TCP=$ssh_tcp_rule, SSH-UDP=$ssh_udp_rule, RDP-TCP=$rdp_tcp_rule, RDP-UDP=$rdp_udp_rule"

            local deny_success=true

            # Add DENY rule for SSH (port 22) - TCP
            log "        🔧 Adding DENY rule for SSH (TCP port 22) as rule $ssh_tcp_rule..."
            local ssh_tcp_result=$(aws ec2 create-network-acl-entry \
                --region "$region" \
                --network-acl-id "$nacl_id" \
                --rule-number "$ssh_tcp_rule" \
                --protocol "6" \
                --rule-action "deny" \
                --ingress \
                --cidr-block "0.0.0.0/0" \
                --port-range "From=22,To=22" 2>&1)

            if [ $? -ne 0 ]; then
                log "        ❌ Failed to create SSH TCP DENY rule: $ssh_tcp_result"
                deny_success=false
            else
                log "        ✅ Added DENY rule $ssh_tcp_rule for SSH (TCP port 22)"
            fi

            # Add DENY rule for RDP (port 3389) - TCP
            log "        🔧 Adding DENY rule for RDP (TCP port 3389) as rule $rdp_tcp_rule..."
            local rdp_tcp_result=$(aws ec2 create-network-acl-entry \
                --region "$region" \
                --network-acl-id "$nacl_id" \
                --rule-number "$rdp_tcp_rule" \
                --protocol "6" \
                --rule-action "deny" \
                --ingress \
                --cidr-block "0.0.0.0/0" \
                --port-range "From=3389,To=3389" 2>&1)

            if [ $? -ne 0 ]; then
                log "        ❌ Failed to create RDP TCP DENY rule: $rdp_tcp_result"
                deny_success=false
            else
                log "        ✅ Added DENY rule $rdp_tcp_rule for RDP (TCP port 3389)"
            fi

            # Add DENY rule for SSH (port 22) - UDP (in case SSH over UDP is used)
            log "        🔧 Adding DENY rule for SSH (UDP port 22) as rule $ssh_udp_rule..."
            local ssh_udp_result=$(aws ec2 create-network-acl-entry \
                --region "$region" \
                --network-acl-id "$nacl_id" \
                --rule-number "$ssh_udp_rule" \
                --protocol "17" \
                --rule-action "deny" \
                --ingress \
                --cidr-block "0.0.0.0/0" \
                --port-range "From=22,To=22" 2>&1)

            if [ $? -ne 0 ]; then
                log "        ❌ Failed to create SSH UDP DENY rule: $ssh_udp_result"
                deny_success=false
            else
                log "        ✅ Added DENY rule $ssh_udp_rule for SSH (UDP port 22)"
            fi

            # Add DENY rule for RDP (port 3389) - UDP
            log "        🔧 Adding DENY rule for RDP (UDP port 3389) as rule $rdp_udp_rule..."
            local rdp_udp_result=$(aws ec2 create-network-acl-entry \
                --region "$region" \
                --network-acl-id "$nacl_id" \
                --rule-number "$rdp_udp_rule" \
                --protocol "17" \
                --rule-action "deny" \
                --ingress \
                --cidr-block "0.0.0.0/0" \
                --port-range "From=3389,To=3389" 2>&1)

            if [ $? -ne 0 ]; then
                log "        ❌ Failed to create RDP UDP DENY rule: $rdp_udp_result"
                deny_success=false
            else
                log "        ✅ Added DENY rule $rdp_udp_rule for RDP (UDP port 3389)"
            fi

            if [ "$deny_success" = true ]; then
                log "        ✅ Successfully added DENY rules for SSH and RDP"
                log "        📋 Added rules: $ssh_tcp_rule (SSH-TCP), $rdp_tcp_rule (RDP-TCP), $ssh_udp_rule (SSH-UDP), $rdp_udp_rule (RDP-UDP)"
                log "        📋 Existing rule $rule_number (Allow All) remains unchanged"
                rules_removed_count=$((rules_removed_count + 1))
                RULES_REMOVED=$((RULES_REMOVED + 1))
                action_taken="${action_taken}ADDED_DENY_RULES_${ssh_tcp_rule}-${rdp_udp_rule};"
            else
                log "        ⚠️  Partial failure adding DENY rules"
                action_taken="${action_taken}PARTIAL_DENY_${rule_number};"
            fi
        fi
    done

    if [ "$dry_run" = "true" ]; then
        log "        [DRY RUN] Would add DENY rules for SSH/RDP on $rules_removed_count NACLs"
        log_summary "$account_id,$region,$nacl_id,$nacl_type,$vpc_id,DRY_RUN,\"$action_taken\""
    elif [ $rules_removed_count -gt 0 ]; then
        log "        ✅ Successfully added DENY rules for SSH/RDP (existing Allow All rules preserved)"
        NACLS_FIXED=$((NACLS_FIXED + 1))
        log_summary "$account_id,$region,$nacl_id,$nacl_type,$vpc_id,FIXED,\"$action_taken\""
    else
        log "        ⚠️  No DENY rules were successfully added"
        log_summary "$account_id,$region,$nacl_id,$nacl_type,$vpc_id,PARTIAL_FAILURE,\"$action_taken\""
    fi

    TOTAL_NACLS_PROCESSED=$((TOTAL_NACLS_PROCESSED + 1))
    return 0
}

# Function to process NACLs in a specific account and region
process_account_region() {
    local account_id=$1
    local region=$2
    local nacl_ids=$3
    local dry_run=$4

    log "    Region: $region"

    # Process each NACL in this region
    for nacl_id in $nacl_ids; do
        if [ -n "$nacl_id" ] && [ "$nacl_id" != "None" ]; then
            fix_nacl_admin_rules "$nacl_id" "$region" "$account_id" "$dry_run"
        fi
    done
}

# Function to process a single account
process_account() {
    local account_id=$1
    local management_account_id=$2
    local dry_run=$3

    log ""
    log "🔧 Processing Account: $account_id"
    TOTAL_ACCOUNTS_PROCESSED=$((TOTAL_ACCOUNTS_PROCESSED + 1))

    # Skip management account (no need to assume role)
    if [ "$account_id" = "$management_account_id" ]; then
        log "  (Management account - using current credentials)"
        process_account_nacls "$account_id" "$dry_run"
        SUCCESSFUL_ACCOUNTS=$((SUCCESSFUL_ACCOUNTS + 1))
    else
        log "  Assuming cross-account role..."
        if assume_role "$account_id"; then
            log "  ✅ Successfully assumed role"
            process_account_nacls "$account_id" "$dry_run"
            reset_credentials
            SUCCESSFUL_ACCOUNTS=$((SUCCESSFUL_ACCOUNTS + 1))
        else
            log "  ❌ Failed to assume role - skipping account"
            FAILED_ACCOUNTS=$((FAILED_ACCOUNTS + 1))
            log_summary "$account_id,ALL,ALL,ROLE_FAILED,UNKNOWN,SKIPPED,Could not assume cross-account role"
            return 1
        fi
    fi
}

# Function to process NACLs for an account (extract from failing resources)
process_account_nacls() {
    local account_id=$1
    local dry_run=$2

    # Extract NACLs for this account from the failing resources file
    # Account IDs in CSV are quoted, so we need to match "account_id"
    local account_nacls=$(grep "\"$account_id\"" "$FAILING_RESOURCES_FILE" | cut -d',' -f3 | sed 's/"//g')

    if [ -z "$account_nacls" ]; then
        log "  No failing NACLs found for this account"
        return 0
    fi

    # Group by region
    declare -A region_nacls

    for arn in $account_nacls; do
        # Extract region and NACL ID from ARN: arn:aws:ec2:REGION:ACCOUNT:network-acl/NACL-ID
        local region=$(echo "$arn" | cut -d':' -f4)
        local nacl_id=$(echo "$arn" | cut -d'/' -f2)

        if [ -n "${region_nacls[$region]}" ]; then
            region_nacls[$region]="${region_nacls[$region]} $nacl_id"
        else
            region_nacls[$region]="$nacl_id"
        fi
    done

    # Process each region
    for region in "${!region_nacls[@]}"; do
        process_account_region "$account_id" "$region" "${region_nacls[$region]}" "$dry_run"
    done
}

# Main execution starts here
echo "=========================================="
echo "NACL Administrative Port Remediation"
echo "=========================================="
echo ""

# Check if failing resources file exists
if [ ! -f "$FAILING_RESOURCES_FILE" ]; then
    echo "❌ Failing resources file not found: $FAILING_RESOURCES_FILE"
    echo "Run the analysis script first: ./analyze-nacl-rules.sh"
    exit 1
fi

# Get target account from command line argument (optional)
TARGET_ACCOUNT=""
if [ $# -eq 1 ]; then
    TARGET_ACCOUNT="$1"
    echo "🎯 Target: Single account ($TARGET_ACCOUNT)"

    # Validate account ID format
    if [[ ! "$TARGET_ACCOUNT" =~ ^[0-9]{12}$ ]]; then
        echo "❌ Invalid account ID format: $TARGET_ACCOUNT"
        echo "Account ID must be exactly 12 digits"
        exit 1
    fi

    # Check if account exists in failing resources
    if ! grep -q "\"$TARGET_ACCOUNT\"" "$FAILING_RESOURCES_FILE"; then
        echo "❌ Account $TARGET_ACCOUNT not found in failing resources"
        echo "Available accounts:"
        tail -n +2 "$FAILING_RESOURCES_FILE" | cut -d',' -f5 | sed 's/"//g' | sort -u
        exit 1
    fi

    ACCOUNTS="$TARGET_ACCOUNT"
else
    echo "🌐 Target: All accounts with NACL findings"
    # Extract unique account IDs from failing resources
    ACCOUNTS=$(tail -n +2 "$FAILING_RESOURCES_FILE" | cut -d',' -f5 | sed 's/"//g' | sort -u)
fi

# Get mode from user
echo ""
echo "Select mode:"
echo "1. Dry run (analyze only, no changes)"
echo "2. Fix NACL rules (remove admin port access)"
if [ -n "$TARGET_ACCOUNT" ]; then
    echo ""
    echo "🎯 Target: Single account ($TARGET_ACCOUNT)"
else
    echo ""
    echo "🌐 Target: All accounts with findings"
fi
read -p "Enter choice (1 or 2): " mode

case $mode in
    1)
        DRY_RUN=true
        echo "Running in DRY RUN mode - no changes will be made"
        ;;
    2)
        DRY_RUN=false
        echo "Running in REMEDIATION mode - NACL rules will be modified"
        echo ""
        echo "⚠️  WARNING: This will add DENY rules for SSH/RDP from internet"
        echo "⚠️  Existing 'allow all' rules will be preserved (no service disruption)"
        echo "⚠️  Backups will be created automatically for rollback"
        echo ""
        read -p "Are you sure? Type 'FIX' to confirm: " confirm
        if [ "$confirm" != "FIX" ]; then
            echo "Operation cancelled."
            exit 0
        fi
        ;;
    *)
        echo "Invalid choice. Exiting."
        exit 1
        ;;
esac

# Initialize reports
log "NACL Administrative Port Remediation Report"
log "Generated: $(date)"
log "Mode: $([ "$DRY_RUN" = "true" ] && echo "DRY RUN" || echo "REMEDIATION")"
log "=========================================="
log ""

# Initialize CSV header
echo "Account,Region,NACL_ID,Type,VPC_ID,Status,Actions_Taken" > "$SUMMARY_REPORT"

# Initialize backup file
echo "[]" > "$BACKUP_REPORT"

# Get management account ID
MANAGEMENT_ACCOUNT_ID=$(aws sts get-caller-identity --query 'Account' --output text)
log "🏢 Management Account ID: $MANAGEMENT_ACCOUNT_ID"
log ""

log "📋 Processing $(echo "$ACCOUNTS" | wc -w) accounts"
log ""

# Process each account
for account in $ACCOUNTS; do
    # Skip empty account IDs
    if [ -z "$account" ] || [ "$account" = "None" ]; then
        continue
    fi

    # Validate account ID format (12 digits)
    if [[ ! "$account" =~ ^[0-9]{12}$ ]]; then
        log "⚠️  Skipping invalid account ID: '$account'"
        continue
    fi

    # Process the account (don't exit on failure)
    process_account "$account" "$MANAGEMENT_ACCOUNT_ID" "$DRY_RUN" || {
        log "⚠️  Account $account processing failed, continuing with next account..."
    }
done

# Generate final statistics
log ""
log "=========================================="
log "REMEDIATION COMPLETE"
log "=========================================="
log ""
log "📊 Summary Statistics:"
log "  • Total accounts processed: $TOTAL_ACCOUNTS_PROCESSED"
log "  • Successful accounts: $SUCCESSFUL_ACCOUNTS"
log "  • Failed accounts: $FAILED_ACCOUNTS"
log "  • Total NACLs processed: $TOTAL_NACLS_PROCESSED"
log "  • NACLs fixed: $NACLS_FIXED"
log "  • NACLs backed up: $NACLS_BACKED_UP"
log "  • DENY rules added: $RULES_REMOVED"
log ""

# Write statistics to separate file
cat > "$STATS_REPORT" << EOF
NACL Administrative Port Remediation Statistics
Generated: $(date)
Mode: $([ "$DRY_RUN" = "true" ] && echo "DRY RUN" || echo "REMEDIATION")
===============================================

Accounts:
  Total processed: $TOTAL_ACCOUNTS_PROCESSED
  Successful: $SUCCESSFUL_ACCOUNTS
  Failed: $FAILED_ACCOUNTS

NACLs:
  Total processed: $TOTAL_NACLS_PROCESSED
  Fixed: $NACLS_FIXED
  Backed up: $NACLS_BACKED_UP

Rules:
  DENY rules added: $RULES_REMOVED

Files generated:
  Detailed report: $DETAILED_REPORT
  Summary CSV: $SUMMARY_REPORT
  Statistics: $STATS_REPORT
  Backups: $BACKUP_REPORT
EOF

log "📁 Reports generated:"
log "  • Detailed report: $DETAILED_REPORT"
log "  • Summary CSV: $SUMMARY_REPORT"
log "  • Statistics: $STATS_REPORT"
if [ "$DRY_RUN" = "false" ]; then
    log "  • Backups: $BACKUP_REPORT"
fi
log ""

if [ $FAILED_ACCOUNTS -gt 0 ]; then
    log "⚠️  Some accounts failed to process. Check the detailed report for issues."
    log "   Common causes: missing cross-account roles, suspended accounts"
fi

if [ "$DRY_RUN" = "true" ]; then
    log "✅ Dry run complete! Review the reports and run in remediation mode when ready."
    log ""
    log "Next steps:"
    log "  1. Review the analysis results"
    log "  2. Test on a single account first: ./test-single-account-nacl.sh <account-id>"
    log "  3. Run in remediation mode: ./fix-admin-port-nacls.sh"
else
    log "✅ Remediation complete!"
    log ""
    log "Next steps:"
    log "  1. Verify the changes in AWS Console"
    log "  2. Test application connectivity if needed"
    log "  3. Run DRATA scan to confirm findings are resolved"
    log "  4. Consider implementing preventive controls (SCP)"
    if [ $NACLS_BACKED_UP -gt 0 ]; then
        log ""
        log "💾 Rollback information:"
        log "  • $NACLS_BACKED_UP NACLs were backed up to: $BACKUP_REPORT"
        log "  • Use ./nacl-backup-restore.sh to rollback if needed"
    fi
fi
