#!/opt/homebrew/bin/bash

# Debug script to understand exactly how DRATA detects NACL violations
# This will help us match their methodology precisely

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo "=========================================="
echo "NACL Detection Methodology Debug"
echo "=========================================="
echo ""

# Check if failing resources file exists
FAILING_RESOURCES_FILE="failing-resources.csv"
if [ ! -f "$FAILING_RESOURCES_FILE" ]; then
    echo "❌ Failing resources file not found: $FAILING_RESOURCES_FILE"
    exit 1
fi

# Get a sample NACL from the failing resources
echo -e "${BLUE}1. Analyzing sample NACLs from DRATA findings...${NC}"
SAMPLE_NACLS=$(head -5 "$FAILING_RESOURCES_FILE" | tail -4)

echo "Sample DRATA findings:"
echo "$SAMPLE_NACLS"
echo ""

# Extract first NACL for detailed analysis
FIRST_NACL_LINE=$(echo "$SAMPLE_NACLS" | head -1)
ACCOUNT_ID=$(echo "$FIRST_NACL_LINE" | cut -d',' -f5 | sed 's/"//g')
NACL_ARN=$(echo "$FIRST_NACL_LINE" | cut -d',' -f3 | sed 's/"//g')
REGION=$(echo "$NACL_ARN" | cut -d':' -f4)
NACL_ID=$(echo "$NACL_ARN" | cut -d'/' -f2)

echo -e "${BLUE}2. Detailed analysis of first NACL:${NC}"
echo "Account: $ACCOUNT_ID"
echo "Region: $REGION"
echo "NACL ID: $NACL_ID"
echo "Full ARN: $NACL_ARN"
echo ""

# Function to assume role and get NACL details
analyze_nacl() {
    local account_id=$1
    local region=$2
    local nacl_id=$3
    
    echo -e "${YELLOW}Analyzing NACL: $nacl_id${NC}"
    
    # Get management account ID
    local management_account_id=$(aws sts get-caller-identity --query 'Account' --output text)
    
    # Assume role if not management account
    if [ "$account_id" != "$management_account_id" ]; then
        echo "  Assuming cross-account role..."
        local role_arn="arn:aws:iam::${account_id}:role/CrossAccountVPCManagementRole"
        local external_id="DrataCleanup2025"
        
        local assume_result=$(aws sts assume-role \
            --role-arn "$role_arn" \
            --role-session-name "NACLDebug-$(date +%s)" \
            --external-id "$external_id" \
            --duration-seconds 900 2>&1)
        
        if [ $? -ne 0 ]; then
            echo "  ❌ Failed to assume role: $assume_result"
            return 1
        fi
        
        export AWS_ACCESS_KEY_ID=$(echo "$assume_result" | jq -r '.Credentials.AccessKeyId')
        export AWS_SECRET_ACCESS_KEY=$(echo "$assume_result" | jq -r '.Credentials.SecretAccessKey')
        export AWS_SESSION_TOKEN=$(echo "$assume_result" | jq -r '.Credentials.SessionToken')
    fi
    
    # Get complete NACL details
    echo "  Fetching NACL details..."
    local nacl_details=$(aws ec2 describe-network-acls \
        --region "$region" \
        --network-acl-ids "$nacl_id" \
        --output json 2>&1)
    
    if [ $? -ne 0 ]; then
        echo "  ❌ Failed to get NACL details: $nacl_details"
        return 1
    fi
    
    # Check if NACL exists
    local nacl_count=$(echo "$nacl_details" | jq '.NetworkAcls | length')
    if [ "$nacl_count" -eq 0 ]; then
        echo "  ❌ NACL not found (may have been deleted)"
        return 1
    fi
    
    # Extract NACL information
    local is_default=$(echo "$nacl_details" | jq -r '.NetworkAcls[0].IsDefault')
    local vpc_id=$(echo "$nacl_details" | jq -r '.NetworkAcls[0].VpcId')
    local entries=$(echo "$nacl_details" | jq '.NetworkAcls[0].Entries')
    
    echo "  ✅ NACL found:"
    echo "    VPC: $vpc_id"
    echo "    Default NACL: $is_default"
    echo ""
    
    # Analyze all entries
    echo "  📋 All NACL Entries:"
    echo "$entries" | jq -r '.[] | "    Rule \(.RuleNumber): \(.RuleAction) \(.Protocol) \(.CidrBlock // .Ipv6CidrBlock // "N/A") ports \(.PortRange.From // "N/A")-\(.PortRange.To // "N/A") egress:\(.Egress)"'
    echo ""
    
    # DRATA-style detection: Inbound rules allowing admin ports from 0.0.0.0/0
    echo "  🔍 DRATA Detection Analysis:"
    echo "    Looking for INBOUND rules (Egress=false) allowing 0.0.0.0/0 to ports 22 or 3389"
    echo ""
    
    # Check for SSH (port 22) violations
    echo "    SSH (Port 22) Analysis:"
    local ssh_violations=$(echo "$entries" | jq -r '
        .[] | select(
            .Egress == false and 
            .RuleAction == "allow" and 
            .CidrBlock == "0.0.0.0/0" and
            (
                (.Protocol == "6" or .Protocol == "-1") and
                (
                    (.PortRange.From <= 22 and .PortRange.To >= 22) or
                    (.PortRange == null)
                )
            )
        ) | "      Rule \(.RuleNumber): Protocol \(.Protocol) Port \(.PortRange.From // "ALL")-\(.PortRange.To // "ALL")"'
    )
    
    if [ -n "$ssh_violations" ]; then
        echo "$ssh_violations"
    else
        echo "      No SSH violations found"
    fi
    
    # Check for RDP (port 3389) violations  
    echo ""
    echo "    RDP (Port 3389) Analysis:"
    local rdp_violations=$(echo "$entries" | jq -r '
        .[] | select(
            .Egress == false and 
            .RuleAction == "allow" and 
            .CidrBlock == "0.0.0.0/0" and
            (
                (.Protocol == "6" or .Protocol == "-1") and
                (
                    (.PortRange.From <= 3389 and .PortRange.To >= 3389) or
                    (.PortRange == null)
                )
            )
        ) | "      Rule \(.RuleNumber): Protocol \(.Protocol) Port \(.PortRange.From // "ALL")-\(.PortRange.To // "ALL")"'
    )
    
    if [ -n "$rdp_violations" ]; then
        echo "$rdp_violations"
    else
        echo "      No RDP violations found"
    fi
    
    # Check for "allow all" rules that would include admin ports
    echo ""
    echo "    'Allow All' Rules Analysis:"
    local allow_all=$(echo "$entries" | jq -r '
        .[] | select(
            .Egress == false and 
            .RuleAction == "allow" and 
            .CidrBlock == "0.0.0.0/0" and
            (.Protocol == "-1" or .PortRange == null)
        ) | "      Rule \(.RuleNumber): Protocol \(.Protocol) - ALLOWS ALL TRAFFIC INCLUDING ADMIN PORTS"'
    )
    
    if [ -n "$allow_all" ]; then
        echo "$allow_all"
    else
        echo "      No 'allow all' rules found"
    fi
    
    echo ""
    echo "  💡 Summary:"
    if [ -n "$ssh_violations" ] || [ -n "$rdp_violations" ] || [ -n "$allow_all" ]; then
        echo "    ⚠️  This NACL DOES have admin port violations (matches DRATA finding)"
    else
        echo "    ✅ This NACL does NOT have admin port violations (contradicts DRATA finding)"
        echo "    🤔 This suggests either:"
        echo "       - DRATA uses different detection logic"
        echo "       - The NACL rules have changed since DRATA scan"
        echo "       - There's an edge case we're missing"
    fi
    
    # Reset credentials if we assumed a role
    if [ "$account_id" != "$management_account_id" ]; then
        unset AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN
    fi
    
    return 0
}

# Analyze the sample NACL
analyze_nacl "$ACCOUNT_ID" "$REGION" "$NACL_ID"

echo ""
echo "=========================================="
echo "Detection Methodology Comparison"
echo "=========================================="
echo ""
echo "DRATA Control Description:"
echo "  'Validates that no NACLs allow unrestricted ingress access'"
echo "  '(source 0.0.0.0/0) to remote server administration ports'"
echo "  'such as port 22 and port 3389, by checking each NACL's inbound rules'"
echo ""
echo "Our Detection Logic:"
echo "  ✅ Inbound rules only (Egress == false)"
echo "  ✅ Allow rules only (RuleAction == 'allow')"
echo "  ✅ Source 0.0.0.0/0 (CidrBlock == '0.0.0.0/0')"
echo "  ✅ TCP protocol (Protocol == '6') or All protocols (Protocol == '-1')"
echo "  ✅ Port ranges covering 22 or 3389"
echo "  ✅ Rules with no port restrictions (PortRange == null)"
echo ""
echo "If our analysis shows no violations but DRATA found them, possible causes:"
echo "  1. NACL rules changed after DRATA scan"
echo "  2. DRATA includes IPv6 rules (we only check IPv4)"
echo "  3. DRATA has different protocol interpretation"
echo "  4. DRATA checks different rule types we missed"
echo ""
echo "💡 Recommendation: Run this on 2-3 more sample NACLs to confirm pattern"
