{"Version": "2012-10-17", "Statement": [{"Sid": "PreventNACLAdminPortsFromInternet", "Effect": "<PERSON><PERSON>", "Action": ["ec2:CreateNetworkAclEntry", "ec2:ReplaceNetworkAclEntry"], "Resource": "*", "Condition": {"Bool": {"aws:ViaAWSService": "false"}, "StringEquals": {"ec2:RuleAction": "allow"}, "IpAddress": {"ec2:CidrBlock": ["0.0.0.0/0"]}, "ForAnyValue:NumericBetween": {"ec2:FromPort": [{"22": "22"}, {"3389": "3389"}]}}}, {"Sid": "PreventNACLAdminPortRangesFromInternet", "Effect": "<PERSON><PERSON>", "Action": ["ec2:CreateNetworkAclEntry", "ec2:ReplaceNetworkAclEntry"], "Resource": "*", "Condition": {"Bool": {"aws:ViaAWSService": "false"}, "StringEquals": {"ec2:RuleAction": "allow"}, "IpAddress": {"ec2:CidrBlock": ["0.0.0.0/0"]}, "ForAnyValue:NumericLessThanEquals": {"ec2:FromPort": ["22", "3389"]}, "ForAnyValue:NumericGreaterThanEquals": {"ec2:ToPort": ["22", "3389"]}}}, {"Sid": "AllowNACLAdminPortsFromPrivateNetworks", "Effect": "Allow", "Action": ["ec2:CreateNetworkAclEntry", "ec2:ReplaceNetworkAclEntry"], "Resource": "*", "Condition": {"IpAddress": {"ec2:CidrBlock": ["10.0.0.0/8", "**********/12", "***********/16"]}}}]}