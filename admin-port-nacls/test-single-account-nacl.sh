#!/opt/homebrew/bin/bash

# Helper script to test NACL remediation on a single account
# Usage: ./test-single-account-nacl.sh <account-id>

if [ $# -ne 1 ]; then
    echo "Usage: $0 <account-id>"
    echo ""
    echo "Examples:"
    echo "  $0 ************    # Test on specific account"
    echo ""
    echo "This script will run NACL analysis and remediation on a single account for testing."
    exit 1
fi

ACCOUNT_ID="$1"

# Validate account ID format
if [[ ! "$ACCOUNT_ID" =~ ^[0-9]{12}$ ]]; then
    echo "❌ Invalid account ID format: $ACCOUNT_ID"
    echo "Account ID must be exactly 12 digits"
    exit 1
fi

echo "=========================================="
echo "Single Account NACL Remediation Test"
echo "=========================================="
echo "Target Account: $ACCOUNT_ID"
echo ""

# Check if the main scripts exist
if [ ! -f "./analyze-nacl-rules.sh" ]; then
    echo "❌ Analysis script not found: ./analyze-nacl-rules.sh"
    echo "Make sure you're running this from the correct directory."
    exit 1
fi

if [ ! -f "./fix-admin-port-nacls.sh" ]; then
    echo "❌ Remediation script not found: ./fix-admin-port-nacls.sh"
    echo "Make sure you're running this from the correct directory."
    exit 1
fi

if [ ! -f "./failing-resources.csv" ]; then
    echo "❌ Failing resources file not found: ./failing-resources.csv"
    echo "Make sure the DRATA findings file is present."
    exit 1
fi

# Check if account exists in failing resources
if ! grep -q "\"$ACCOUNT_ID\"" "./failing-resources.csv"; then
    echo "❌ Account $ACCOUNT_ID not found in failing resources"
    echo ""
    echo "Available accounts with NACL findings:"
    tail -n +2 "./failing-resources.csv" | cut -d',' -f5 | sed 's/"//g' | sort -u | head -10
    echo ""
    echo "Use one of the accounts listed above."
    exit 1
fi

# Make sure the scripts are executable
chmod +x ./analyze-nacl-rules.sh
chmod +x ./fix-admin-port-nacls.sh

echo "🔍 Step 1: Running analysis on account $ACCOUNT_ID..."
echo ""

# Create a temporary failing resources file with just this account
TEMP_FAILING_FILE="failing-resources-temp-$ACCOUNT_ID.csv"
head -n 1 "./failing-resources.csv" > "$TEMP_FAILING_FILE"
grep "\"$ACCOUNT_ID\"" "./failing-resources.csv" >> "$TEMP_FAILING_FILE"

# Backup original file and use temp file
mv "./failing-resources.csv" "./failing-resources.csv.backup"
mv "$TEMP_FAILING_FILE" "./failing-resources.csv"

# Run analysis
./analyze-nacl-rules.sh

echo ""
echo "🔧 Step 2: Running dry-run remediation on account $ACCOUNT_ID..."
echo ""

# Run dry-run remediation
echo "1" | ./fix-admin-port-nacls.sh "$ACCOUNT_ID"

echo ""
echo "=========================================="
echo "Analysis Complete"
echo "=========================================="
echo ""

# Show recent reports
REPORTS_DIR="nacl-reports"
if [ -d "$REPORTS_DIR" ]; then
    echo "📊 Generated reports:"
    ls -lt "$REPORTS_DIR"/*$ACCOUNT_ID* 2>/dev/null || ls -lt "$REPORTS_DIR"/*$(date +%Y%m%d)* 2>/dev/null | head -5
    echo ""

    # Show summary of latest analysis
    LATEST_SUMMARY=$(ls -t "$REPORTS_DIR"/nacl-analysis-summary-*.csv 2>/dev/null | head -1)
    if [ -f "$LATEST_SUMMARY" ]; then
        echo "📋 Summary for account $ACCOUNT_ID:"
        echo "Account,Region,NACL_ID,Type,VPC_ID,Status,Admin_Rules"
        grep "^$ACCOUNT_ID," "$LATEST_SUMMARY" | head -10

        NACL_COUNT=$(grep -c "^$ACCOUNT_ID," "$LATEST_SUMMARY")
        echo ""
        echo "Found $NACL_COUNT NACLs with issues in account $ACCOUNT_ID"
    fi
fi

echo ""
echo "🎯 Next Steps:"
echo ""
echo "1. Review the analysis results above"
echo "2. Check the detailed reports in the nacl-reports/ directory"
echo "3. If everything looks good, run actual remediation:"
echo "   echo '2' | ./fix-admin-port-nacls.sh $ACCOUNT_ID"
echo ""
echo "4. Or test on more development accounts before production"
echo ""

# Restore original failing resources file
mv "./failing-resources.csv.backup" "./failing-resources.csv"

echo "✅ Single account test complete!"
echo ""
echo "⚠️  Remember: This was a DRY RUN - no actual changes were made"
echo "   Use the remediation command above to make actual changes"
