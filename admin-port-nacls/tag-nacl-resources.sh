#!/opt/homebrew/bin/bash

# Tag NACL resources to identify which are managed by Terraform
# This script adds a "Terraform_Managed" column to the failing-resources.csv

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
FAILING_RESOURCES_FILE="failing-resources.csv"
ENHANCED_FILE="failing-resources-enhanced.csv"

echo "=========================================="
echo "NACL Terraform Management Tagging"
echo "=========================================="
echo ""

# Check if failing resources file exists
if [ ! -f "$FAILING_RESOURCES_FILE" ]; then
    echo "❌ Failing resources file not found: $FAILING_RESOURCES_FILE"
    exit 1
fi

echo -e "${BLUE}📋 Processing failing resources file...${NC}"
echo "Input file: $FAILING_RESOURCES_FILE"
echo "Output file: $ENHANCED_FILE"
echo ""

# Store original credentials at script start
ORIGINAL_AWS_ACCESS_KEY_ID="$AWS_ACCESS_KEY_ID"
ORIGINAL_AWS_SECRET_ACCESS_KEY="$AWS_SECRET_ACCESS_KEY"
ORIGINAL_AWS_SESSION_TOKEN="$AWS_SESSION_TOKEN"

# Function to reset credentials to original
reset_credentials() {
    if [ -n "$ORIGINAL_AWS_ACCESS_KEY_ID" ]; then
        export AWS_ACCESS_KEY_ID="$ORIGINAL_AWS_ACCESS_KEY_ID"
    else
        unset AWS_ACCESS_KEY_ID
    fi

    if [ -n "$ORIGINAL_AWS_SECRET_ACCESS_KEY" ]; then
        export AWS_SECRET_ACCESS_KEY="$ORIGINAL_AWS_SECRET_ACCESS_KEY"
    else
        unset AWS_SECRET_ACCESS_KEY
    fi

    if [ -n "$ORIGINAL_AWS_SESSION_TOKEN" ]; then
        export AWS_SESSION_TOKEN="$ORIGINAL_AWS_SESSION_TOKEN"
    else
        unset AWS_SESSION_TOKEN
    fi
}

# Function to assume cross-account role
assume_role() {
    local account_id=$1
    local role_name="CrossAccountVPCManagementRole"
    local external_id="DrataCleanup2025"

    local role_arn="arn:aws:iam::${account_id}:role/${role_name}"

    # Assume the role and export credentials
    local assume_result=$(aws sts assume-role \
        --role-arn "$role_arn" \
        --role-session-name "NACLTagging-$(date +%s)" \
        --external-id "$external_id" \
        --duration-seconds 3600 2>&1)

    local exit_code=$?

    if [ $exit_code -ne 0 ]; then
        echo "    ❌ Role assumption failed: $assume_result"
        return 1
    fi

    # Validate JSON response
    if ! echo "$assume_result" | jq -e '.Credentials' >/dev/null 2>&1; then
        echo "    ❌ Invalid JSON response from assume-role"
        return 1
    fi

    # Export the credentials for subsequent AWS CLI calls
    export AWS_ACCESS_KEY_ID=$(echo "$assume_result" | jq -r '.Credentials.AccessKeyId')
    export AWS_SECRET_ACCESS_KEY=$(echo "$assume_result" | jq -r '.Credentials.SecretAccessKey')
    export AWS_SESSION_TOKEN=$(echo "$assume_result" | jq -r '.Credentials.SessionToken')

    return 0
}

# Function to check if NACL is managed by Terraform
check_terraform_managed() {
    local nacl_id=$1
    local region=$2
    local account_id=$3

    # Get NACL tags
    local tags_result=$(aws ec2 describe-tags \
        --region "$region" \
        --filters "Name=resource-id,Values=$nacl_id" "Name=resource-type,Values=network-acl" \
        --output json 2>&1)

    local exit_code=$?

    if [ $exit_code -ne 0 ]; then
        echo "    ❌ Failed to get tags for $nacl_id: $tags_result" >&2
        echo "ERROR"
        return 1
    fi

    # Check for managed_by=Terraform tag
    local terraform_managed=$(echo "$tags_result" | jq -r '
        .Tags[] | select(.Key == "managed_by" and .Value == "Terraform") | .Value'
    )

    if [ "$terraform_managed" = "Terraform" ]; then
        echo "    ✅ $nacl_id is managed by Terraform" >&2
        echo "YES"
    else
        echo "    📋 $nacl_id is NOT managed by Terraform" >&2
        echo "NO"
    fi

    return 0
}

# Function to process a single account
process_account() {
    local account_id=$1
    local management_account_id=$2

    echo ""
    echo -e "${YELLOW}🔍 Processing Account: $account_id${NC}"

    # Skip management account (no need to assume role)
    if [ "$account_id" = "$management_account_id" ]; then
        echo "  (Management account - using current credentials)"
        process_account_nacls "$account_id"
    else
        echo "  Assuming cross-account role..."
        if assume_role "$account_id"; then
            echo "  ✅ Successfully assumed role"
            process_account_nacls "$account_id"
            reset_credentials
        else
            echo "  ❌ Failed to assume role - marking NACLs as ERROR"
            # Mark all NACLs for this account as ERROR
            mark_account_nacls_error "$account_id"
            return 1
        fi
    fi
}

# Function to process NACLs for an account
process_account_nacls() {
    local account_id=$1

    # Extract NACLs for this account from the failing resources file
    local account_nacls=$(grep "\"$account_id\"" "$FAILING_RESOURCES_FILE" | cut -d',' -f3 | sed 's/"//g')

    if [ -z "$account_nacls" ]; then
        echo "  No failing NACLs found for this account"
        return 0
    fi

    echo "  Found $(echo "$account_nacls" | wc -w) NACLs to check"

    for arn in $account_nacls; do
        # Extract region and NACL ID from ARN: arn:aws:ec2:REGION:ACCOUNT:network-acl/NACL-ID
        local region=$(echo "$arn" | cut -d':' -f4)
        local nacl_id=$(echo "$arn" | cut -d'/' -f2)

        echo "    Checking $nacl_id in $region..."
        local terraform_managed=$(check_terraform_managed "$nacl_id" "$region" "$account_id")

        # Store the result for later CSV update
        echo "$account_id,$nacl_id,$terraform_managed" >> "/tmp/nacl_terraform_status.tmp"
    done
}

# Function to mark all NACLs for an account as ERROR
mark_account_nacls_error() {
    local account_id=$1

    # Extract NACLs for this account from the failing resources file
    local account_nacls=$(grep "\"$account_id\"" "$FAILING_RESOURCES_FILE" | cut -d',' -f3 | sed 's/"//g')

    for arn in $account_nacls; do
        local nacl_id=$(echo "$arn" | cut -d'/' -f2)
        echo "$account_id,$nacl_id,ERROR" >> "/tmp/nacl_terraform_status.tmp"
    done
}

# Main execution starts here
echo -e "${BLUE}🔍 Checking prerequisites...${NC}"

# Check if jq is available
if ! command -v jq >/dev/null 2>&1; then
    echo "❌ jq is not installed"
    exit 1
fi
echo "✅ jq is available"

# Check AWS credentials
MANAGEMENT_ACCOUNT_ID=$(aws sts get-caller-identity --query 'Account' --output text 2>&1)
IDENTITY_EXIT_CODE=$?

if [ $IDENTITY_EXIT_CODE -ne 0 ]; then
    echo "❌ Failed to get AWS identity: $MANAGEMENT_ACCOUNT_ID"
    exit 1
fi
echo "✅ AWS credentials are working"
echo "🏢 Management Account ID: $MANAGEMENT_ACCOUNT_ID"

# Initialize temporary file for storing results
echo "" > "/tmp/nacl_terraform_status.tmp"

# Extract unique account IDs from failing resources
ACCOUNTS=$(tail -n +2 "$FAILING_RESOURCES_FILE" | cut -d',' -f5 | sed 's/"//g' | sort -u)
ACCOUNT_COUNT=$(echo "$ACCOUNTS" | wc -w)

echo ""
echo -e "${BLUE}📊 Found $ACCOUNT_COUNT accounts to process${NC}"

# Process each account
for account in $ACCOUNTS; do
    # Skip empty account IDs
    if [ -z "$account" ] || [ "$account" = "None" ]; then
        continue
    fi

    # Validate account ID format (12 digits)
    if [[ ! "$account" =~ ^[0-9]{12}$ ]]; then
        echo "⚠️  Skipping invalid account ID: '$account'"
        continue
    fi

    # Process the account
    process_account "$account" "$MANAGEMENT_ACCOUNT_ID"
done

echo ""
echo -e "${BLUE}📝 Creating enhanced CSV file...${NC}"

# Create the enhanced CSV with the new column
# First, copy the header and add the new column
head -n 1 "$FAILING_RESOURCES_FILE" | sed 's/$/,Terraform_Managed/' > "$ENHANCED_FILE"

# Process each line of the original CSV (skip header)
tail -n +2 "$FAILING_RESOURCES_FILE" | while IFS= read -r line; do
    # Skip empty lines
    if [ -z "$line" ]; then
        continue
    fi

    # Extract account ID and NACL ID from the line
    account_id=$(echo "$line" | cut -d',' -f5 | sed 's/"//g')
    nacl_arn=$(echo "$line" | cut -d',' -f3 | sed 's/"//g')
    nacl_id=$(echo "$nacl_arn" | cut -d'/' -f2)

    # Look up the Terraform status
    terraform_status=$(grep "^$account_id,$nacl_id," "/tmp/nacl_terraform_status.tmp" | cut -d',' -f3)

    # If no status found, mark as UNKNOWN
    if [ -z "$terraform_status" ]; then
        terraform_status="UNKNOWN"
    fi

    # Add the new column to the line
    echo "$line,$terraform_status" >> "$ENHANCED_FILE"
done

# Clean up temporary file
rm -f "/tmp/nacl_terraform_status.tmp"

echo ""
echo "=========================================="
echo "TAGGING COMPLETE"
echo "=========================================="
echo ""

# Show summary statistics
TOTAL_NACLS=$(tail -n +2 "$ENHANCED_FILE" | wc -l)
TERRAFORM_MANAGED=$(tail -n +2 "$ENHANCED_FILE" | cut -d',' -f10 | grep -c "YES" || echo "0")
NOT_TERRAFORM_MANAGED=$(tail -n +2 "$ENHANCED_FILE" | cut -d',' -f10 | grep -c "NO" || echo "0")
ERROR_COUNT=$(tail -n +2 "$ENHANCED_FILE" | cut -d',' -f10 | grep -c "ERROR" || echo "0")
UNKNOWN_COUNT=$(tail -n +2 "$ENHANCED_FILE" | cut -d',' -f10 | grep -c "UNKNOWN" || echo "0")

echo -e "${GREEN}📊 Summary Statistics:${NC}"
echo "  • Total NACLs: $TOTAL_NACLS"
echo "  • Terraform managed: $TERRAFORM_MANAGED"
echo "  • NOT Terraform managed: $NOT_TERRAFORM_MANAGED"
echo "  • Errors: $ERROR_COUNT"
echo "  • Unknown: $UNKNOWN_COUNT"
echo ""

echo -e "${GREEN}📁 Files generated:${NC}"
echo "  • Enhanced CSV: $ENHANCED_FILE"
echo ""

if [ $NOT_TERRAFORM_MANAGED -gt 0 ]; then
    echo -e "${YELLOW}💡 Next steps:${NC}"
    echo "  1. Review the enhanced CSV file"
    echo "  2. Use the terraform-safe remediation script (coming next)"
    echo "  3. Only NACLs marked as 'NO' will be modified"
else
    echo -e "${YELLOW}⚠️  All NACLs appear to be Terraform managed or had errors${NC}"
    echo "   Review the enhanced CSV before proceeding"
fi

echo ""
echo "✅ NACL tagging complete!"
