#!/opt/homebrew/bin/bash

# Test script to verify the Terraform tagging logic works correctly

echo "Testing Terraform tagging logic..."

# Create a mock function that simulates the check_terraform_managed function
check_terraform_managed_mock() {
    local nacl_id=$1
    local region=$2
    local account_id=$3
    
    echo "    Checking $nacl_id in $region..." >&2
    
    # Simulate different responses based on NACL ID
    case $nacl_id in
        "acl-terraform1")
            echo "    ✅ $nacl_id is managed by Terraform" >&2
            echo "YES"
            ;;
        "acl-terraform2")
            echo "    ✅ $nacl_id is managed by Terraform" >&2
            echo "YES"
            ;;
        "acl-error1")
            echo "    ❌ Failed to get tags for $nacl_id: Permission denied" >&2
            echo "ERROR"
            ;;
        *)
            echo "    📋 $nacl_id is NOT managed by Terraform" >&2
            echo "NO"
            ;;
    esac
    
    return 0
}

# Test the function
echo ""
echo "Testing function output capture:"

echo "Test 1: Terraform managed NACL"
result1=$(check_terraform_managed_mock "acl-terraform1" "us-east-1" "************")
echo "Captured result: '$result1'"

echo ""
echo "Test 2: Non-Terraform managed NACL"
result2=$(check_terraform_managed_mock "acl-manual1" "us-east-1" "************")
echo "Captured result: '$result2'"

echo ""
echo "Test 3: Error case"
result3=$(check_terraform_managed_mock "acl-error1" "us-east-1" "************")
echo "Captured result: '$result3'"

echo ""
echo "Creating test CSV processing..."

# Create a test CSV
cat > test-input.csv << 'EOF'
"Provider Name","Client ID or Client Alias","Resource ARN","Org Unit ID","Account ID","Account Name","Resource Name","Failing Message","Associated Help Article"
"AWS Organizational Units","************","arn:aws:ec2:us-east-1:************:network-acl/acl-terraform1","ou-test","************","test-account","acl-terraform1","Test message","https://example.com"
"AWS Organizational Units","************","arn:aws:ec2:us-east-1:************:network-acl/acl-manual1","ou-test","************","test-account","acl-manual1","Test message","https://example.com"
"AWS Organizational Units","************","arn:aws:ec2:us-east-1:************:network-acl/acl-error1","ou-test","************","test-account","acl-error1","Test message","https://example.com"
"AWS Organizational Units","************","arn:aws:ec2:us-east-1:************:network-acl/acl-terraform2","ou-test","************","test-account","acl-terraform2","Test message","https://example.com"
EOF

# Simulate the tagging process
echo "" > /tmp/test_nacl_status.tmp

# Process each NACL
echo "Processing test NACLs..."
tail -n +2 test-input.csv | while IFS= read -r line; do
    if [ -n "$line" ]; then
        account_id=$(echo "$line" | cut -d',' -f5 | sed 's/"//g')
        nacl_arn=$(echo "$line" | cut -d',' -f3 | sed 's/"//g')
        nacl_id=$(echo "$nacl_arn" | cut -d'/' -f2)
        region=$(echo "$nacl_arn" | cut -d':' -f4)
        
        terraform_status=$(check_terraform_managed_mock "$nacl_id" "$region" "$account_id")
        echo "$account_id,$nacl_id,$terraform_status" >> /tmp/test_nacl_status.tmp
    fi
done

echo ""
echo "Results stored in temporary file:"
cat /tmp/test_nacl_status.tmp

echo ""
echo "Creating enhanced CSV..."

# Create enhanced CSV
head -n 1 test-input.csv | sed 's/$/,Terraform_Managed/' > test-output.csv

tail -n +2 test-input.csv | while IFS= read -r line; do
    if [ -n "$line" ]; then
        account_id=$(echo "$line" | cut -d',' -f5 | sed 's/"//g')
        nacl_arn=$(echo "$line" | cut -d',' -f3 | sed 's/"//g')
        nacl_id=$(echo "$nacl_arn" | cut -d'/' -f2)
        
        terraform_status=$(grep "^$account_id,$nacl_id," /tmp/test_nacl_status.tmp | cut -d',' -f3)
        
        if [ -z "$terraform_status" ]; then
            terraform_status="UNKNOWN"
        fi
        
        echo "$line,$terraform_status" >> test-output.csv
    fi
done

echo ""
echo "Final enhanced CSV:"
cat test-output.csv

echo ""
echo "Summary statistics:"
TOTAL=$(tail -n +2 test-output.csv | wc -l)
TERRAFORM_MANAGED=$(tail -n +2 test-output.csv | cut -d',' -f10 | grep -c "YES" || echo "0")
NOT_TERRAFORM_MANAGED=$(tail -n +2 test-output.csv | cut -d',' -f10 | grep -c "NO" || echo "0")
ERROR_COUNT=$(tail -n +2 test-output.csv | cut -d',' -f10 | grep -c "ERROR" || echo "0")

echo "  • Total: $TOTAL"
echo "  • Terraform managed: $TERRAFORM_MANAGED"
echo "  • NOT Terraform managed: $NOT_TERRAFORM_MANAGED"
echo "  • Errors: $ERROR_COUNT"

# Clean up
rm -f test-input.csv test-output.csv /tmp/test_nacl_status.tmp

echo ""
echo "✅ Test completed successfully!"
