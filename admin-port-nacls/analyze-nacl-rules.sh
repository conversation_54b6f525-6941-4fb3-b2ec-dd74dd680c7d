#!/opt/homebrew/bin/bash

# Analyze NACL rules for administrative port access across all accounts
# This script examines the current NACL configurations to understand the security findings

# Note: We don't use 'set -e' here because we want to continue processing other accounts
# even if some accounts fail (e.g., suspended accounts, role assumption failures)

# Configuration
ROOT_OU_ID="r-3g6q"
FAILING_RESOURCES_FILE="failing-resources.csv"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Create reports directory
REPORTS_DIR="nacl-reports"
mkdir -p "$REPORTS_DIR"

# Generate timestamp for this run
TIMESTAMP=$(date +"%Y%m%d-%H%M%S")
DETAILED_REPORT="$REPORTS_DIR/nacl-analysis-report-$TIMESTAMP.txt"
SUMMARY_REPORT="$REPORTS_DIR/nacl-analysis-summary-$TIMESTAMP.csv"
STATS_REPORT="$REPORTS_DIR/nacl-analysis-stats-$TIMESTAMP.txt"

# Initialize counters
TOTAL_ACCOUNTS_PROCESSED=0
SUCCESSFUL_ACCOUNTS=0
FAILED_ACCOUNTS=0
TOTAL_NACLS_ANALYZED=0
NACLS_WITH_ADMIN_PORTS=0
DEFAULT_NACLS_COUNT=0
CUSTOM_NACLS_COUNT=0

# Function to log messages
log() {
    local message="$1"
    echo -e "$message" | tee -a "$DETAILED_REPORT"
}

# Function to log summary data
log_summary() {
    echo "$1" >> "$SUMMARY_REPORT"
}

# Function to assume cross-account role
assume_role() {
    local account_id=$1
    local role_name="CrossAccountVPCManagementRole"
    local external_id="DrataCleanup2025"

    local role_arn="arn:aws:iam::${account_id}:role/${role_name}"

    log "    Attempting to assume role: $role_arn"
    log "    External ID: $external_id"

    # Assume the role and capture both stdout and stderr
    local assume_result=$(aws sts assume-role \
        --role-arn "$role_arn" \
        --role-session-name "NACLAnalysis-$(date +%s)" \
        --external-id "$external_id" \
        --duration-seconds 3600 2>&1)

    local exit_code=$?

    if [ $exit_code -ne 0 ]; then
        log "    ❌ Role assumption failed with exit code: $exit_code"
        log "    Error details: $assume_result"

        # Check for common error patterns
        if echo "$assume_result" | grep -q "AccessDenied"; then
            log "    → This appears to be an access denied error"
            log "    → Check if the role exists and trusts the management account"
        elif echo "$assume_result" | grep -q "InvalidUserID.NotFound"; then
            log "    → The role does not exist in this account"
            log "    → Run: ../cross-account-roles/check-role-deployment.sh"
        elif echo "$assume_result" | grep -q "TokenRefreshRequired"; then
            log "    → AWS credentials may have expired"
            log "    → Try refreshing your AWS session"
        elif echo "$assume_result" | grep -q "UnauthorizedOperation"; then
            log "    → Insufficient permissions in management account"
        else
            log "    → Unknown error - see details above"
        fi

        return 1
    fi

    # Validate JSON response
    if ! echo "$assume_result" | jq -e '.Credentials' >/dev/null 2>&1; then
        log "    ❌ Invalid JSON response from assume-role"
        log "    Response: $assume_result"
        return 1
    fi

    # Export the credentials for subsequent AWS CLI calls
    export AWS_ACCESS_KEY_ID=$(echo "$assume_result" | jq -r '.Credentials.AccessKeyId')
    export AWS_SECRET_ACCESS_KEY=$(echo "$assume_result" | jq -r '.Credentials.SecretAccessKey')
    export AWS_SESSION_TOKEN=$(echo "$assume_result" | jq -r '.Credentials.SessionToken')

    # Validate credentials work
    local test_result=$(aws sts get-caller-identity --query 'Account' --output text 2>&1)
    local test_exit_code=$?

    if [ $test_exit_code -ne 0 ]; then
        log "    ❌ Assumed role credentials don't work"
        log "    Test error: $test_result"
        return 1
    fi

    if [ "$test_result" != "$account_id" ]; then
        log "    ❌ Assumed role is for wrong account"
        log "    Expected: $account_id, Got: $test_result"
        return 1
    fi

    log "    ✅ Successfully assumed role and validated credentials"
    return 0
}

# Store original credentials at script start
ORIGINAL_AWS_ACCESS_KEY_ID="$AWS_ACCESS_KEY_ID"
ORIGINAL_AWS_SECRET_ACCESS_KEY="$AWS_SECRET_ACCESS_KEY"
ORIGINAL_AWS_SESSION_TOKEN="$AWS_SESSION_TOKEN"

# Function to reset credentials to original
reset_credentials() {
    if [ -n "$ORIGINAL_AWS_ACCESS_KEY_ID" ]; then
        export AWS_ACCESS_KEY_ID="$ORIGINAL_AWS_ACCESS_KEY_ID"
    else
        unset AWS_ACCESS_KEY_ID
    fi

    if [ -n "$ORIGINAL_AWS_SECRET_ACCESS_KEY" ]; then
        export AWS_SECRET_ACCESS_KEY="$ORIGINAL_AWS_SECRET_ACCESS_KEY"
    else
        unset AWS_SECRET_ACCESS_KEY
    fi

    if [ -n "$ORIGINAL_AWS_SESSION_TOKEN" ]; then
        export AWS_SESSION_TOKEN="$ORIGINAL_AWS_SESSION_TOKEN"
    else
        unset AWS_SESSION_TOKEN
    fi
}

# Function to check if NACL has admin port rules
check_admin_port_rules() {
    local nacl_id=$1
    local region=$2
    local account_id=$3

    log "      Analyzing NACL rules for $nacl_id in region $region..."

    # Test basic EC2 access first
    local ec2_test=$(aws ec2 describe-regions --region "$region" --query 'Regions[0].RegionName' --output text 2>&1)
    local ec2_exit_code=$?

    if [ $ec2_exit_code -ne 0 ]; then
        log "        ❌ Cannot access EC2 in region $region"
        log "        Error: $ec2_test"
        log_summary "$account_id,$region,$nacl_id,ERROR,EC2_ACCESS_FAILED,Cannot access EC2 service in region"
        return 1
    fi

    # Get NACL entries with detailed error handling
    local nacl_result=$(aws ec2 describe-network-acls \
        --region "$region" \
        --network-acl-ids "$nacl_id" \
        --output json 2>&1)
    local nacl_exit_code=$?

    if [ $nacl_exit_code -ne 0 ]; then
        log "        ❌ Failed to retrieve NACL $nacl_id"
        log "        Error: $nacl_result"

        if echo "$nacl_result" | grep -q "InvalidNetworkAclID.NotFound"; then
            log "        → NACL does not exist (may have been deleted)"
            log_summary "$account_id,$region,$nacl_id,ERROR,NACL_NOT_FOUND,NACL does not exist"
        elif echo "$nacl_result" | grep -q "UnauthorizedOperation"; then
            log "        → Insufficient permissions to describe NACLs"
            log_summary "$account_id,$region,$nacl_id,ERROR,PERMISSION_DENIED,Insufficient permissions"
        else
            log "        → Unknown error retrieving NACL"
            log_summary "$account_id,$region,$nacl_id,ERROR,UNKNOWN_ERROR,Failed to retrieve NACL"
        fi
        return 1
    fi

    # Validate JSON and extract entries
    if ! echo "$nacl_result" | jq -e '.NetworkAcls[0]' >/dev/null 2>&1; then
        log "        ❌ Invalid response format or NACL not found"
        log "        Response: $nacl_result"
        log_summary "$account_id,$region,$nacl_id,ERROR,INVALID_RESPONSE,Invalid API response"
        return 1
    fi

    local nacl_entries=$(echo "$nacl_result" | jq '.NetworkAcls[0].Entries')

    if [ "$nacl_entries" = "null" ] || [ "$nacl_entries" = "[]" ]; then
        log "        ❌ No NACL entries found"
        log_summary "$account_id,$region,$nacl_id,ERROR,NO_ENTRIES,NACL has no entries"
        return 1
    fi

    # Check if it's a default NACL
    local is_default=$(aws ec2 describe-network-acls \
        --region "$region" \
        --network-acl-ids "$nacl_id" \
        --query 'NetworkAcls[0].IsDefault' \
        --output text 2>/dev/null || echo "false")

    local nacl_type="CUSTOM"
    if [ "$is_default" = "true" ]; then
        nacl_type="DEFAULT"
        DEFAULT_NACLS_COUNT=$((DEFAULT_NACLS_COUNT + 1))
    else
        CUSTOM_NACLS_COUNT=$((CUSTOM_NACLS_COUNT + 1))
    fi

    # Get VPC ID for context
    local vpc_id=$(aws ec2 describe-network-acls \
        --region "$region" \
        --network-acl-ids "$nacl_id" \
        --query 'NetworkAcls[0].VpcId' \
        --output text 2>/dev/null || echo "unknown")

    # Check for SSH (22) and RDP (3389) rules allowing 0.0.0.0/0
    # DRATA methodology: inbound rules with TCP(6), UDP(17), or ALL(-1) protocols
    local has_ssh_rule=false
    local has_rdp_rule=false
    local admin_rules_found=""

    # Parse entries and look for problematic rules - DRATA exact methodology
    # SSH (port 22) violations: inbound rules allowing 0.0.0.0/0 with TCP/UDP/ALL protocols covering port 22
    local ssh_rules=$(echo "$nacl_entries" | jq -r '
        .[] | select(
            .Egress == false and
            .RuleAction == "allow" and
            .CidrBlock == "0.0.0.0/0" and
            (.Protocol == "6" or .Protocol == "17" or .Protocol == "-1") and
            (
                (.PortRange.From <= 22 and .PortRange.To >= 22) or
                (.PortRange == null)
            )
        ) | .RuleNumber'
    )

    # RDP (port 3389) violations: inbound rules allowing 0.0.0.0/0 with TCP/UDP/ALL protocols covering port 3389
    local rdp_rules=$(echo "$nacl_entries" | jq -r '
        .[] | select(
            .Egress == false and
            .RuleAction == "allow" and
            .CidrBlock == "0.0.0.0/0" and
            (.Protocol == "6" or .Protocol == "17" or .Protocol == "-1") and
            (
                (.PortRange.From <= 3389 and .PortRange.To >= 3389) or
                (.PortRange == null)
            )
        ) | .RuleNumber'
    )

    if [ -n "$ssh_rules" ]; then
        has_ssh_rule=true
        admin_rules_found="${admin_rules_found}SSH(22);"
        log "        🔍 Found SSH rule(s): $ssh_rules"
    fi

    if [ -n "$rdp_rules" ]; then
        has_rdp_rule=true
        admin_rules_found="${admin_rules_found}RDP(3389);"
        log "        🔍 Found RDP rule(s): $rdp_rules"
    fi

    if [ "$has_ssh_rule" = true ] || [ "$has_rdp_rule" = true ]; then
        NACLS_WITH_ADMIN_PORTS=$((NACLS_WITH_ADMIN_PORTS + 1))
        log "        ⚠️  NACL has admin port access from internet"
        log_summary "$account_id,$region,$nacl_id,$nacl_type,$vpc_id,HAS_ADMIN_PORTS,\"$admin_rules_found\""
    else
        log "        ✅ No admin port rules found (unexpected - should be in failing list)"
        log_summary "$account_id,$region,$nacl_id,$nacl_type,$vpc_id,NO_ADMIN_PORTS,None"
    fi

    TOTAL_NACLS_ANALYZED=$((TOTAL_NACLS_ANALYZED + 1))
    return 0
}

# Function to process NACLs in a specific account and region
process_account_region() {
    local account_id=$1
    local region=$2
    local nacl_ids=$3

    log "    Region: $region"

    # Process each NACL in this region
    for nacl_id in $nacl_ids; do
        if [ -n "$nacl_id" ] && [ "$nacl_id" != "None" ]; then
            check_admin_port_rules "$nacl_id" "$region" "$account_id"
        fi
    done
}

# Function to process a single account
process_account() {
    local account_id=$1
    local management_account_id=$2

    log ""
    log "🔍 Processing Account: $account_id"
    TOTAL_ACCOUNTS_PROCESSED=$((TOTAL_ACCOUNTS_PROCESSED + 1))

    # Skip management account (no need to assume role)
    if [ "$account_id" = "$management_account_id" ]; then
        log "  (Management account - using current credentials)"
        process_account_nacls "$account_id"
        SUCCESSFUL_ACCOUNTS=$((SUCCESSFUL_ACCOUNTS + 1))
    else
        log "  Assuming cross-account role..."
        if assume_role "$account_id"; then
            log "  ✅ Successfully assumed role"
            process_account_nacls "$account_id"
            reset_credentials
            SUCCESSFUL_ACCOUNTS=$((SUCCESSFUL_ACCOUNTS + 1))
        else
            log "  ❌ Failed to assume role - skipping account"
            FAILED_ACCOUNTS=$((FAILED_ACCOUNTS + 1))
            log_summary "$account_id,ALL,ALL,ROLE_FAILED,UNKNOWN,SKIPPED,Could not assume cross-account role"
            return 1
        fi
    fi
}

# Function to process NACLs for an account (extract from failing resources)
process_account_nacls() {
    local account_id=$1

    # Extract NACLs for this account from the failing resources file
    # Account IDs in CSV are quoted, so we need to match "account_id"
    local account_nacls=$(grep "\"$account_id\"" "$FAILING_RESOURCES_FILE" | cut -d',' -f3 | sed 's/"//g')

    if [ -z "$account_nacls" ]; then
        log "  No failing NACLs found for this account"
        return 0
    fi

    # Group by region (bash 3.x compatible approach)
    local regions_found=""

    for arn in $account_nacls; do
        # Extract region and NACL ID from ARN: arn:aws:ec2:REGION:ACCOUNT:network-acl/NACL-ID
        local region=$(echo "$arn" | cut -d':' -f4)
        local nacl_id=$(echo "$arn" | cut -d'/' -f2)

        log "    Found NACL: $nacl_id in region $region"

        # Add region to list if not already there
        if ! echo "$regions_found" | grep -q "$region"; then
            regions_found="$regions_found $region"
        fi
    done

    # Process each region
    for region in $regions_found; do
        if [ -n "$region" ]; then
            # Get all NACLs for this region
            local region_nacls=""
            for arn in $account_nacls; do
                local arn_region=$(echo "$arn" | cut -d':' -f4)
                if [ "$arn_region" = "$region" ]; then
                    local nacl_id=$(echo "$arn" | cut -d'/' -f2)
                    region_nacls="$region_nacls $nacl_id"
                fi
            done

            # Process this region's NACLs
            process_account_region "$account_id" "$region" "$region_nacls"
        fi
    done
}

# Main execution starts here
echo "=========================================="
echo "NACL Administrative Port Analysis"
echo "=========================================="
echo ""

# Check prerequisites
echo "🔍 Checking prerequisites..."

# Check if jq is available
if ! command -v jq >/dev/null 2>&1; then
    echo "❌ jq is not installed"
    echo "Install with: brew install jq (macOS) or apt-get install jq (Linux)"
    exit 1
fi
echo "✅ jq is available"

# Check if failing resources file exists
if [ ! -f "$FAILING_RESOURCES_FILE" ]; then
    echo "❌ Failing resources file not found: $FAILING_RESOURCES_FILE"
    echo "Make sure the DRATA findings file is in this directory"
    exit 1
fi
echo "✅ Failing resources file found"

# Check AWS credentials
MANAGEMENT_ACCOUNT_ID=$(aws sts get-caller-identity --query 'Account' --output text 2>&1)
IDENTITY_EXIT_CODE=$?

if [ $IDENTITY_EXIT_CODE -ne 0 ]; then
    echo "❌ Failed to get AWS identity"
    echo "Error: $MANAGEMENT_ACCOUNT_ID"
    echo ""
    echo "Please check:"
    echo "  - AWS CLI is configured"
    echo "  - AWS credentials are valid"
    echo "  - Internet connection is working"
    exit 1
fi
echo "✅ AWS credentials are working"

# Check if we have any accounts to process
ACCOUNT_COUNT=$(tail -n +2 "$FAILING_RESOURCES_FILE" | cut -d',' -f5 | sed 's/"//g' | sort -u | wc -l)
if [ "$ACCOUNT_COUNT" -eq 0 ]; then
    echo "❌ No accounts found in failing resources file"
    exit 1
fi
echo "✅ Found $ACCOUNT_COUNT accounts to process"

echo ""
echo "If you encounter cross-account access issues, run:"
echo "  ./debug-cross-account-access.sh"
echo ""

# Initialize reports
log "NACL Administrative Port Analysis Report"
log "Generated: $(date)"
log "Management Account: $MANAGEMENT_ACCOUNT_ID"
log "Accounts to process: $ACCOUNT_COUNT"
log "=========================================="
log ""

# Initialize CSV header
echo "Account,Region,NACL_ID,Type,VPC_ID,Status,Admin_Rules" > "$SUMMARY_REPORT"

log "📋 Loading failing resources from: $FAILING_RESOURCES_FILE"
log "🏢 Management Account ID: $MANAGEMENT_ACCOUNT_ID"
log ""

# Extract unique account IDs from failing resources
ACCOUNTS=$(tail -n +2 "$FAILING_RESOURCES_FILE" | cut -d',' -f5 | sed 's/"//g' | sort -u)

log "📊 Found $(echo "$ACCOUNTS" | wc -w) unique accounts with NACL findings"
log ""

# Process each account
for account in $ACCOUNTS; do
    # Skip empty account IDs
    if [ -z "$account" ] || [ "$account" = "None" ]; then
        continue
    fi

    # Validate account ID format (12 digits)
    if [[ ! "$account" =~ ^[0-9]{12}$ ]]; then
        log "⚠️  Skipping invalid account ID: '$account'"
        continue
    fi

    # Process the account (don't exit on failure)
    process_account "$account" "$MANAGEMENT_ACCOUNT_ID" || {
        log "⚠️  Account $account processing failed, continuing with next account..."
    }
done

# Generate final statistics
log ""
log "=========================================="
log "ANALYSIS COMPLETE"
log "=========================================="
log ""
log "📊 Summary Statistics:"
log "  • Total accounts processed: $TOTAL_ACCOUNTS_PROCESSED"
log "  • Successful accounts: $SUCCESSFUL_ACCOUNTS"
log "  • Failed accounts: $FAILED_ACCOUNTS"
log "  • Total NACLs analyzed: $TOTAL_NACLS_ANALYZED"
log "  • NACLs with admin ports: $NACLS_WITH_ADMIN_PORTS"
log "  • Default NACLs: $DEFAULT_NACLS_COUNT"
log "  • Custom NACLs: $CUSTOM_NACLS_COUNT"
log ""

# Write statistics to separate file
cat > "$STATS_REPORT" << EOF
NACL Administrative Port Analysis Statistics
Generated: $(date)
==========================================

Accounts:
  Total processed: $TOTAL_ACCOUNTS_PROCESSED
  Successful: $SUCCESSFUL_ACCOUNTS
  Failed: $FAILED_ACCOUNTS

NACLs:
  Total analyzed: $TOTAL_NACLS_ANALYZED
  With admin ports: $NACLS_WITH_ADMIN_PORTS
  Default NACLs: $DEFAULT_NACLS_COUNT
  Custom NACLs: $CUSTOM_NACLS_COUNT

Files generated:
  Detailed report: $DETAILED_REPORT
  Summary CSV: $SUMMARY_REPORT
  Statistics: $STATS_REPORT
EOF

log "📁 Reports generated:"
log "  • Detailed report: $DETAILED_REPORT"
log "  • Summary CSV: $SUMMARY_REPORT"
log "  • Statistics: $STATS_REPORT"
log ""

if [ $FAILED_ACCOUNTS -gt 0 ]; then
    log "⚠️  Some accounts failed to process. Check the detailed report for issues."
    log "   Common causes: missing cross-account roles, suspended accounts"
fi

log "✅ Analysis complete! Review the reports before proceeding with remediation."
