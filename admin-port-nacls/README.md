# NACL Administrative Port Remediation Toolkit

This toolkit helps remediate DRATA security findings related to Network Access Control Lists (NACLs) that allow administrative traffic (SSH port 22 and RDP port 3389) from the internet (0.0.0.0/0).

## 🎯 Purpose

Fix security findings where NACLs allow unrestricted access to:
- **SSH (port 22)** - Secure Shell access
- **RDP (port 3389)** - Remote Desktop Protocol access

These ports should not be accessible from the internet for security compliance.

## 📁 Toolkit Components

### Core Scripts
- **`analyze-nacl-rules.sh`** - Analyze current NACL configurations
- **`tag-nacl-resources.sh`** - Check which NACLs are managed by Terraform
- **`fix-admin-port-nacls-terraform-safe.sh`** - Terraform-safe remediation (RECOMMENDED)
- **`fix-admin-port-nacls.sh`** - Main remediation script (all NACLs)
- **`test-single-account-nacl.sh`** - Test on a single account first
- **`nacl-backup-restore.sh`** - Backup and restore utility for rollbacks

### Prevention & Monitoring
- **`prevent-admin-nacl-scp.json`** - Service Control Policy to prevent future issues
- **`nacl-config-rule.yaml`** - AWS Config rule for ongoing monitoring

### Utilities
- **`view-nacl-reports.sh`** - View and manage reports
- **`failing-resources.csv`** - Input file with DRATA findings

### Reports Directory
- **`nacl-reports/`** - All execution reports and compliance evidence

## 🚀 Quick Start

### Prerequisites
1. **Cross-account roles** must be deployed (from `../cross-account-roles/`)
2. **AWS CLI** configured with management account access
3. **jq** installed for JSON processing
4. **DRATA findings file** (`failing-resources.csv`) in this directory

### Basic Workflow

1. **Analyze the current state:**
   ```bash
   chmod +x *.sh
   ./analyze-nacl-rules.sh
   ```

2. **Check Terraform management status:**
   ```bash
   ./tag-nacl-resources.sh
   ```

3. **Test on a single account first:**
   ```bash
   ./test-single-account-nacl.sh ************
   ```

4. **Run Terraform-safe remediation (RECOMMENDED):**
   ```bash
   # Dry-run first - only processes non-Terraform managed NACLs
   echo "1" | ./fix-admin-port-nacls-terraform-safe.sh

   # Run actual remediation on safe NACLs only
   echo "2" | ./fix-admin-port-nacls-terraform-safe.sh
   ```

5. **Alternative: Run on ALL NACLs (use with caution):**
   ```bash
   ./fix-admin-port-nacls.sh
   # Select option 1 for dry-run, then option 2 for remediation
   ```

6. **View results:**
   ```bash
   ./view-nacl-reports.sh
   ```

## 📊 Understanding the Reports

### Analysis Reports
- **Detailed Report** (`nacl-analysis-report-*.txt`) - Complete analysis log
- **Summary CSV** (`nacl-analysis-summary-*.csv`) - Structured data for spreadsheets
- **Statistics** (`nacl-analysis-stats-*.txt`) - High-level summary

### Remediation Reports
- **Detailed Report** (`nacl-remediation-report-*.txt`) - Complete remediation log
- **Summary CSV** (`nacl-remediation-summary-*.csv`) - Actions taken per NACL
- **Backup File** (`nacl-backups-*.json`) - Complete NACL configurations for rollback

## 🔧 Advanced Usage

### Single Account Testing
```bash
# Test specific account
./test-single-account-nacl.sh ************

# Run remediation on specific account
./fix-admin-port-nacls.sh ************
```

### Backup Management
```bash
# List available backups
./nacl-backup-restore.sh list

# Show backup details
./nacl-backup-restore.sh show nacl-reports/nacl-backups-********-123456.json

# Restore single NACL
./nacl-backup-restore.sh restore backup-file.json ************ acl-********

# Restore all NACLs from backup (dry-run)
./nacl-backup-restore.sh restore-all backup-file.json dry-run
```

### Report Management
```bash
# View recent reports
./view-nacl-reports.sh list

# Search for specific account/NACL
./view-nacl-reports.sh search ************

# Show account summary
./view-nacl-reports.sh account ************
```

## 🛡️ Safety Features

### Automatic Backups
- Complete NACL configurations backed up before changes
- JSON format for easy restoration
- Timestamped for audit trail

### Dry-Run Mode
- Test all operations without making changes
- Validate cross-account access
- Preview what would be changed

### Rollback Capability
- Restore individual NACLs or entire backups
- Preserve original rule configurations
- Support for partial rollbacks

### Comprehensive Logging
- Detailed logs of all operations
- CSV summaries for compliance reporting
- Statistics for management reporting

## 🔒 Security Considerations

### Terraform-Safe Approach (RECOMMENDED)
The toolkit can identify and skip NACLs managed by Terraform:
- ✅ **Checks for `managed_by=Terraform` tag** on each NACL
- ✅ **Only modifies non-Terraform managed NACLs** (safe to change)
- ✅ **Skips Terraform-managed NACLs** (should be fixed via Terraform code)
- ✅ **Prevents infrastructure drift** and Terraform conflicts

### What This Toolkit Does
- ✅ Adds DENY rules for SSH (port 22) and RDP (port 3389) from 0.0.0.0/0
- ✅ Preserves existing "Allow All" rules (zero service disruption)
- ✅ Creates complete backups for rollback
- ✅ Provides detailed audit logs

### What This Toolkit Does NOT Do
- ❌ Does not modify Security Groups
- ❌ Does not affect private network access (10.0.0.0/8, etc.)
- ❌ Does not remove legitimate business rules
- ❌ Does not modify default NACL behavior beyond admin ports

### Impact Assessment
- **Low Risk**: Most NACLs are default and allow all traffic
- **Medium Risk**: Custom NACLs may have specific business rules
- **Recommendation**: Test on development accounts first

## 🚨 Prevention (Post-Remediation)

### 1. Service Control Policy
Deploy the SCP to prevent future admin port rules:
```bash
# Review the policy
cat prevent-admin-nacl-scp.json

# Deploy via AWS Organizations console or CLI
aws organizations attach-policy \
  --policy-id p-xxxxxxxxxx \
  --target-id ou-3g6q-xxxxxxxx
```

### 2. AWS Config Monitoring
Deploy Config rules for ongoing monitoring:
```bash
# Deploy the CloudFormation template
aws cloudformation create-stack \
  --stack-name nacl-monitoring \
  --template-body file://nacl-config-rule.yaml \
  --capabilities CAPABILITY_IAM
```

## 📋 Troubleshooting

### Common Issues

**Cross-account role assumption fails:**
- Verify roles are deployed: `../cross-account-roles/check-role-deployment.sh`
- Check account is not suspended
- Verify external ID matches

**NACL not found:**
- Account may have been cleaned up
- NACL may have been deleted
- Check region - NACLs are region-specific

**Permission denied:**
- Verify cross-account role has EC2 permissions
- Check if account has restrictive SCPs
- Ensure management account has proper permissions

### Getting Help
1. Check detailed logs in `nacl-reports/`
2. Use dry-run mode to test without changes
3. Test on single account first
4. Review backup files before restoration

## 📈 Compliance & Audit

### Evidence Files
All operations generate compliance evidence:
- **Detailed logs** - Complete audit trail
- **CSV summaries** - Structured data for reporting
- **Backup files** - Proof of safe change management
- **Statistics** - Management reporting

### Retention
- Keep all files for audit purposes
- Use `./view-nacl-reports.sh clean` to manage old files
- Consider archiving to S3 for long-term retention

## 🔗 Integration

This toolkit integrates with your existing AWS automation:
- **Cross-account roles** - Reuses existing infrastructure
- **Report structure** - Consistent with VPC cleanup toolkit
- **Safety patterns** - Same dry-run and backup approach
- **Compliance focus** - Designed for audit requirements

## 📞 Support

For issues or questions:
1. Review this README
2. Check the troubleshooting section
3. Examine detailed logs in `nacl-reports/`
4. Test with single account first
5. Use dry-run mode to validate operations
