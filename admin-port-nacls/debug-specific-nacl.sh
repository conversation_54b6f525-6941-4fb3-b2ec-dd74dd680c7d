#!/opt/homebrew/bin/bash

# Debug script to examine the specific NACL that DRATA flagged
# NACL: acl-06d9b37b915ff3a79 in account ************, region us-east-1

ACCOUNT_ID="************"
REGION="us-east-1"
NACL_ID="acl-06d9b37b915ff3a79"

echo "=========================================="
echo "Debugging Specific NACL"
echo "=========================================="
echo "Account: $ACCOUNT_ID"
echo "Region: $REGION"
echo "NACL: $NACL_ID"
echo ""

# Get management account ID
MANAGEMENT_ACCOUNT_ID=$(aws sts get-caller-identity --query 'Account' --output text)
echo "Management Account: $MANAGEMENT_ACCOUNT_ID"

# Assume cross-account role
echo ""
echo "🔑 Assuming cross-account role..."
ROLE_ARN="arn:aws:iam::${ACCOUNT_ID}:role/CrossAccountVPCManagementRole"
EXTERNAL_ID="DrataCleanup2025"

ASSUME_RESULT=$(aws sts assume-role \
    --role-arn "$ROLE_ARN" \
    --role-session-name "NACLDebug-$(date +%s)" \
    --external-id "$EXTERNAL_ID" \
    --duration-seconds 900 2>&1)

if [ $? -ne 0 ]; then
    echo "❌ Failed to assume role: $ASSUME_RESULT"
    exit 1
fi

export AWS_ACCESS_KEY_ID=$(echo "$ASSUME_RESULT" | jq -r '.Credentials.AccessKeyId')
export AWS_SECRET_ACCESS_KEY=$(echo "$ASSUME_RESULT" | jq -r '.Credentials.SecretAccessKey')
export AWS_SESSION_TOKEN=$(echo "$ASSUME_RESULT" | jq -r '.Credentials.SessionToken')

echo "✅ Successfully assumed role"

# Get NACL details
echo ""
echo "📋 Fetching NACL details..."
NACL_DETAILS=$(aws ec2 describe-network-acls \
    --region "$REGION" \
    --network-acl-ids "$NACL_ID" \
    --output json 2>&1)

if [ $? -ne 0 ]; then
    echo "❌ Failed to get NACL details: $NACL_DETAILS"
    exit 1
fi

# Check if NACL exists
NACL_COUNT=$(echo "$NACL_DETAILS" | jq '.NetworkAcls | length')
if [ "$NACL_COUNT" -eq 0 ]; then
    echo "❌ NACL not found"
    exit 1
fi

echo "✅ NACL found"

# Extract basic info
IS_DEFAULT=$(echo "$NACL_DETAILS" | jq -r '.NetworkAcls[0].IsDefault')
VPC_ID=$(echo "$NACL_DETAILS" | jq -r '.NetworkAcls[0].VpcId')
ENTRIES=$(echo "$NACL_DETAILS" | jq '.NetworkAcls[0].Entries')

echo ""
echo "📊 NACL Information:"
echo "  VPC ID: $VPC_ID"
echo "  Is Default: $IS_DEFAULT"
echo ""

# Show all entries in detail
echo "📋 All NACL Entries (Raw API Response):"
echo "$ENTRIES" | jq '.'
echo ""

# Show entries in human-readable format
echo "📋 NACL Entries (Human Readable):"
echo "Rule# | Egress | Action | Protocol | CIDR        | Port Range"
echo "------|--------|--------|----------|-------------|------------"
echo "$ENTRIES" | jq -r '.[] | "\(.RuleNumber | tostring | . + (6 - length) * " ") | \(.Egress | tostring | . + (6 - length) * " ") | \(.RuleAction | . + (6 - length) * " ") | \(.Protocol | . + (8 - length) * " ") | \(.CidrBlock // .Ipv6CidrBlock // "N/A" | . + (11 - length) * " ") | \(.PortRange.From // "ALL")-\(.PortRange.To // "ALL")"'
echo ""

# Test our current detection logic
echo "🔍 Testing Our Current Detection Logic:"
echo ""

# SSH detection
echo "SSH (Port 22) Detection:"
SSH_RULES=$(echo "$ENTRIES" | jq -r '
    .[] | select(
        .Egress == false and
        .RuleAction == "allow" and 
        .CidrBlock == "0.0.0.0/0" and
        (.Protocol == "6" or .Protocol == "17" or .Protocol == "-1") and
        (
            (.PortRange.From <= 22 and .PortRange.To >= 22) or
            (.PortRange == null)
        )
    ) | "  Rule \(.RuleNumber): Protocol \(.Protocol), Ports \(.PortRange.From // "ALL")-\(.PortRange.To // "ALL")"'
)

if [ -n "$SSH_RULES" ]; then
    echo "  ⚠️  SSH violations found:"
    echo "$SSH_RULES"
else
    echo "  ✅ No SSH violations detected"
fi

echo ""

# RDP detection
echo "RDP (Port 3389) Detection:"
RDP_RULES=$(echo "$ENTRIES" | jq -r '
    .[] | select(
        .Egress == false and
        .RuleAction == "allow" and 
        .CidrBlock == "0.0.0.0/0" and
        (.Protocol == "6" or .Protocol == "17" or .Protocol == "-1") and
        (
            (.PortRange.From <= 3389 and .PortRange.To >= 3389) or
            (.PortRange == null)
        )
    ) | "  Rule \(.RuleNumber): Protocol \(.Protocol), Ports \(.PortRange.From // "ALL")-\(.PortRange.To // "ALL")"'
)

if [ -n "$RDP_RULES" ]; then
    echo "  ⚠️  RDP violations found:"
    echo "$RDP_RULES"
else
    echo "  ✅ No RDP violations detected"
fi

echo ""

# Debug each condition separately
echo "🔬 Debugging Detection Conditions:"
echo ""

echo "1. Inbound rules (Egress == false):"
echo "$ENTRIES" | jq -r '.[] | select(.Egress == false) | "  Rule \(.RuleNumber): Egress=\(.Egress)"'
echo ""

echo "2. Allow rules:"
echo "$ENTRIES" | jq -r '.[] | select(.Egress == false and .RuleAction == "allow") | "  Rule \(.RuleNumber): Action=\(.RuleAction)"'
echo ""

echo "3. Source 0.0.0.0/0:"
echo "$ENTRIES" | jq -r '.[] | select(.Egress == false and .RuleAction == "allow" and .CidrBlock == "0.0.0.0/0") | "  Rule \(.RuleNumber): CIDR=\(.CidrBlock)"'
echo ""

echo "4. Admin protocols (TCP/UDP/ALL):"
echo "$ENTRIES" | jq -r '.[] | select(.Egress == false and .RuleAction == "allow" and .CidrBlock == "0.0.0.0/0" and (.Protocol == "6" or .Protocol == "17" or .Protocol == "-1")) | "  Rule \(.RuleNumber): Protocol=\(.Protocol)"'
echo ""

echo "5. Port conditions:"
echo "$ENTRIES" | jq -r '.[] | select(.Egress == false and .RuleAction == "allow" and .CidrBlock == "0.0.0.0/0" and (.Protocol == "6" or .Protocol == "17" or .Protocol == "-1")) | "  Rule \(.RuleNumber): PortRange=\(.PortRange // "null")"'
echo ""

# Final analysis
echo "=========================================="
echo "Analysis Summary"
echo "=========================================="

TOTAL_VIOLATIONS=$(echo "$ENTRIES" | jq -r '
    [.[] | select(
        .Egress == false and
        .RuleAction == "allow" and 
        .CidrBlock == "0.0.0.0/0" and
        (.Protocol == "6" or .Protocol == "17" or .Protocol == "-1") and
        (
            (.PortRange.From <= 22 and .PortRange.To >= 22) or
            (.PortRange.From <= 3389 and .PortRange.To >= 3389) or
            (.PortRange == null)
        )
    )] | length'
)

echo "Total admin port violations detected: $TOTAL_VIOLATIONS"
echo ""

if [ "$TOTAL_VIOLATIONS" -gt 0 ]; then
    echo "✅ Our detection logic SHOULD find this NACL"
    echo "❓ If the main script shows 0 violations, there's a bug in the main script"
else
    echo "❌ Our detection logic is NOT finding this NACL"
    echo "🔧 We need to fix our detection conditions"
fi

# Reset credentials
unset AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN
