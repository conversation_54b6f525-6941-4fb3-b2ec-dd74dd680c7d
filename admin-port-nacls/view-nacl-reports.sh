#!/opt/homebrew/bin/bash

# Helper script to view and manage NACL remediation reports
# This helps you navigate the compliance evidence files

REPORTS_DIR="nacl-reports"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo "=========================================="
echo "NACL Remediation Reports Manager"
echo "=========================================="

# Check if reports directory exists
if [ ! -d "$REPORTS_DIR" ]; then
    echo -e "${RED}❌ Reports directory not found: $REPORTS_DIR${NC}"
    echo "Run the NACL analysis or remediation scripts first to generate reports."
    exit 1
fi

# Function to show recent reports
show_recent_reports() {
    echo -e "${BLUE}📊 Recent NACL Reports:${NC}"
    echo "----------------------------------------"
    
    # Analysis reports
    echo "Analysis Reports:"
    ls -lt "$REPORTS_DIR"/nacl-analysis-*.txt 2>/dev/null | head -5 | while read line; do
        echo "  📋 $(echo "$line" | awk '{print $9}') ($(echo "$line" | awk '{print $6, $7, $8}'))"
    done
    
    echo ""
    
    # Remediation reports
    echo "Remediation Reports:"
    ls -lt "$REPORTS_DIR"/nacl-remediation-*.txt 2>/dev/null | head -5 | while read line; do
        echo "  🔧 $(echo "$line" | awk '{print $9}') ($(echo "$line" | awk '{print $6, $7, $8}'))"
    done
    
    echo ""
    
    # Backup files
    echo "Backup Files:"
    ls -lt "$REPORTS_DIR"/nacl-backups-*.json 2>/dev/null | head -5 | while read line; do
        local file=$(echo "$line" | awk '{print $9}')
        local count=$(jq length "$file" 2>/dev/null || echo "0")
        echo "  💾 $(basename "$file") ($count NACLs backed up)"
    done
}

# Function to show summary statistics
show_summary() {
    echo ""
    echo -e "${BLUE}📈 Summary Statistics:${NC}"
    echo "----------------------------------------"
    
    # Count different types of reports
    local analysis_count=$(ls "$REPORTS_DIR"/nacl-analysis-*.txt 2>/dev/null | wc -l)
    local remediation_count=$(ls "$REPORTS_DIR"/nacl-remediation-*.txt 2>/dev/null | wc -l)
    local backup_count=$(ls "$REPORTS_DIR"/nacl-backups-*.json 2>/dev/null | wc -l)
    
    echo "Report Types:"
    echo "  Analysis runs: $analysis_count"
    echo "  Remediation runs: $remediation_count"
    echo "  Backup files: $backup_count"
    
    # Show latest statistics if available
    local latest_stats=$(ls -t "$REPORTS_DIR"/nacl-*-stats-*.txt 2>/dev/null | head -1)
    if [ -f "$latest_stats" ]; then
        echo ""
        echo "Latest Run Statistics ($(basename "$latest_stats")):"
        grep -E "(Total|Successful|Failed|Fixed|Backed up)" "$latest_stats" | sed 's/^/  /'
    fi
    
    # Show total NACLs backed up
    local total_backed_up=0
    for backup_file in "$REPORTS_DIR"/nacl-backups-*.json; do
        if [ -f "$backup_file" ]; then
            local count=$(jq length "$backup_file" 2>/dev/null || echo "0")
            total_backed_up=$((total_backed_up + count))
        fi
    done
    
    if [ $total_backed_up -gt 0 ]; then
        echo ""
        echo "Backup Summary:"
        echo "  Total NACLs backed up: $total_backed_up"
        echo "  💡 Use ./nacl-backup-restore.sh to manage backups"
    fi
}

# Function to open latest reports
open_latest() {
    echo -e "${BLUE}📖 Opening Latest Reports:${NC}"
    echo "----------------------------------------"
    
    # Find latest analysis report
    local latest_analysis=$(ls -t "$REPORTS_DIR"/nacl-analysis-report-*.txt 2>/dev/null | head -1)
    if [ -f "$latest_analysis" ]; then
        echo "Latest Analysis Report: $latest_analysis"
        if command -v less >/dev/null 2>&1; then
            echo "Opening with 'less' (press 'q' to quit)..."
            less "$latest_analysis"
        else
            echo "Use: cat '$latest_analysis' | head -50 to view"
        fi
    fi
    
    # Find latest remediation report
    local latest_remediation=$(ls -t "$REPORTS_DIR"/nacl-remediation-report-*.txt 2>/dev/null | head -1)
    if [ -f "$latest_remediation" ]; then
        echo ""
        echo "Latest Remediation Report: $latest_remediation"
        if command -v less >/dev/null 2>&1; then
            echo "Opening with 'less' (press 'q' to quit)..."
            less "$latest_remediation"
        else
            echo "Use: cat '$latest_remediation' | head -50 to view"
        fi
    fi
    
    # Show latest CSV summary
    local latest_csv=$(ls -t "$REPORTS_DIR"/nacl-*-summary-*.csv 2>/dev/null | head -1)
    if [ -f "$latest_csv" ]; then
        echo ""
        echo "Latest CSV Summary: $latest_csv"
        echo "First 10 rows:"
        head -11 "$latest_csv" | column -t -s','
    fi
}

# Function to search reports
search_reports() {
    local search_term="$1"
    
    if [ -z "$search_term" ]; then
        read -p "Enter search term (account ID, NACL ID, etc.): " search_term
    fi
    
    if [ -z "$search_term" ]; then
        echo "No search term provided"
        return 1
    fi
    
    echo -e "${BLUE}🔍 Searching for: '$search_term'${NC}"
    echo "----------------------------------------"
    
    # Search in text reports
    echo "Text Reports:"
    grep -l "$search_term" "$REPORTS_DIR"/*.txt 2>/dev/null | while read file; do
        echo "  📄 $(basename "$file")"
        grep -n "$search_term" "$file" | head -3 | sed 's/^/    /'
        echo ""
    done
    
    # Search in CSV files
    echo "CSV Reports:"
    grep -l "$search_term" "$REPORTS_DIR"/*.csv 2>/dev/null | while read file; do
        echo "  📊 $(basename "$file")"
        grep "$search_term" "$file" | head -3 | sed 's/^/    /'
        echo ""
    done
    
    # Search in backup files
    echo "Backup Files:"
    for backup_file in "$REPORTS_DIR"/nacl-backups-*.json; do
        if [ -f "$backup_file" ] && jq -e --arg term "$search_term" '.[] | select(.account == $term or .backup.NetworkAclId == $term or .backup.VpcId == $term)' "$backup_file" >/dev/null 2>&1; then
            echo "  💾 $(basename "$backup_file")"
            jq --arg term "$search_term" '.[] | select(.account == $term or .backup.NetworkAclId == $term or .backup.VpcId == $term) | "    Account: \(.account) NACL: \(.backup.NetworkAclId) VPC: \(.backup.VpcId)"' "$backup_file" -r
            echo ""
        fi
    done
}

# Function to show account summary
show_account_summary() {
    local account_id="$1"
    
    if [ -z "$account_id" ]; then
        read -p "Enter account ID: " account_id
    fi
    
    if [[ ! "$account_id" =~ ^[0-9]{12}$ ]]; then
        echo "❌ Invalid account ID format: $account_id"
        return 1
    fi
    
    echo -e "${BLUE}📋 Account Summary: $account_id${NC}"
    echo "----------------------------------------"
    
    # Find all mentions of this account
    local found=false
    
    # Check CSV files
    for csv_file in "$REPORTS_DIR"/*.csv; do
        if [ -f "$csv_file" ] && grep -q "^$account_id," "$csv_file"; then
            found=true
            echo "$(basename "$csv_file"):"
            echo "Account,Region,NACL_ID,Type,VPC_ID,Status,Details"
            grep "^$account_id," "$csv_file" | head -10
            echo ""
        fi
    done
    
    # Check backup files
    for backup_file in "$REPORTS_DIR"/nacl-backups-*.json; do
        if [ -f "$backup_file" ]; then
            local nacl_count=$(jq --arg account "$account_id" '[.[] | select(.account == $account)] | length' "$backup_file")
            if [ "$nacl_count" -gt 0 ]; then
                found=true
                echo "Backup file $(basename "$backup_file"): $nacl_count NACLs"
                jq --arg account "$account_id" '.[] | select(.account == $account) | "  NACL: \(.backup.NetworkAclId) VPC: \(.backup.VpcId) Timestamp: \(.timestamp)"' "$backup_file" -r
                echo ""
            fi
        fi
    done
    
    if [ "$found" = false ]; then
        echo "No data found for account $account_id"
    fi
}

# Main menu
if [ $# -eq 0 ]; then
    show_recent_reports
    show_summary
    
    echo ""
    echo -e "${YELLOW}Commands:${NC}"
    echo "  $0 list         - Show recent reports"
    echo "  $0 summary      - Show summary statistics"
    echo "  $0 latest       - Open/view latest reports"
    echo "  $0 search       - Search reports for specific terms"
    echo "  $0 account      - Show summary for specific account"
    echo "  $0 backups      - Manage backup files"
    echo "  $0 clean        - Clean up old reports (interactive)"
    echo ""
    echo "💡 Tip: All files in $REPORTS_DIR/ are compliance evidence"
    echo "   Keep them for audit purposes!"
    
elif [ "$1" = "list" ]; then
    show_recent_reports
    
elif [ "$1" = "summary" ]; then
    show_summary
    
elif [ "$1" = "latest" ]; then
    open_latest
    
elif [ "$1" = "search" ]; then
    search_reports "$2"
    
elif [ "$1" = "account" ]; then
    show_account_summary "$2"
    
elif [ "$1" = "backups" ]; then
    echo "Launching backup manager..."
    ./nacl-backup-restore.sh list
    
elif [ "$1" = "clean" ]; then
    echo "Clean up old reports:"
    echo "----------------------------------------"
    echo "⚠️  This will permanently delete old report files!"
    echo ""
    echo "Reports older than how many days should be deleted?"
    read -p "Enter number of days (or 'cancel' to abort): " days
    
    if [ "$days" = "cancel" ]; then
        echo "Cleanup cancelled."
        exit 0
    fi
    
    if [[ "$days" =~ ^[0-9]+$ ]] && [ "$days" -gt 0 ]; then
        echo ""
        echo "Files that would be deleted (older than $days days):"
        find "$REPORTS_DIR" -name "*.txt" -o -name "*.csv" -o -name "*.json" | \
            xargs ls -lt | awk -v days="$days" '
            BEGIN{cutoff=systime()-days*86400} 
            {
                cmd="stat -f %m " $9
                cmd | getline file_time
                close(cmd)
                if(file_time < cutoff) print $9
            }'
        
        echo ""
        read -p "Proceed with deletion? (y/N): " confirm
        if [[ "$confirm" =~ ^[Yy]$ ]]; then
            find "$REPORTS_DIR" -name "*.txt" -o -name "*.csv" -o -name "*.json" | \
                xargs ls -lt | awk -v days="$days" '
                BEGIN{cutoff=systime()-days*86400} 
                {
                    cmd="stat -f %m " $9
                    cmd | getline file_time
                    close(cmd)
                    if(file_time < cutoff) system("rm " $9)
                }'
            echo "✅ Cleanup complete"
        else
            echo "Cleanup cancelled"
        fi
    else
        echo "❌ Invalid input. Please enter a positive number."
    fi
    
else
    echo "❌ Unknown command: $1"
    echo "Use: $0 (without arguments) to see available commands"
fi
