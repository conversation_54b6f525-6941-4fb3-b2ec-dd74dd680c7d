[{"account": "************", "timestamp": "2025-05-30T08:26:56Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-********", "NetworkAclId": "acl-06d9b37b915ff3a79", "SubnetId": "subnet-045464242a5e4a14b"}, {"NetworkAclAssociationId": "aclassoc-547f6e11", "NetworkAclId": "acl-06d9b37b915ff3a79", "SubnetId": "subnet-00956ca7593c3a9a4"}, {"NetworkAclAssociationId": "aclassoc-475f4e02", "NetworkAclId": "acl-06d9b37b915ff3a79", "SubnetId": "subnet-0ff76764c89f4eba8"}, {"NetworkAclAssociationId": "aclassoc-44edfb01", "NetworkAclId": "acl-06d9b37b915ff3a79", "SubnetId": "subnet-0993ed6ed0affc808"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-06d9b37b915ff3a79", "Tags": [], "VpcId": "vpc-0d8fd8c4a0f53fa5c", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T08:27:05Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-917916df", "NetworkAclId": "acl-abcd5bd6", "SubnetId": "subnet-f07ddebd"}, {"NetworkAclAssociationId": "aclassoc-907916de", "NetworkAclId": "acl-abcd5bd6", "SubnetId": "subnet-987665ff"}, {"NetworkAclAssociationId": "aclassoc-ac7916e2", "NetworkAclId": "acl-abcd5bd6", "SubnetId": "subnet-10a2852e"}, {"NetworkAclAssociationId": "aclassoc-ad7916e3", "NetworkAclId": "acl-abcd5bd6", "SubnetId": "subnet-7296885c"}, {"NetworkAclAssociationId": "aclassoc-ae7916e0", "NetworkAclId": "acl-abcd5bd6", "SubnetId": "subnet-3ac5d966"}, {"NetworkAclAssociationId": "aclassoc-af7916e1", "NetworkAclId": "acl-abcd5bd6", "SubnetId": "subnet-b4fb2eba"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-abcd5bd6", "Tags": [], "VpcId": "vpc-e076599a", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T08:27:14Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-06ffd26e53cc309b4", "NetworkAclId": "acl-06ae18b1881404116", "SubnetId": "subnet-0fb728d3639e5ea70"}, {"NetworkAclAssociationId": "aclassoc-0ef9c7f03a698a36c", "NetworkAclId": "acl-06ae18b1881404116", "SubnetId": "subnet-0c2505ecc8badfc27"}, {"NetworkAclAssociationId": "aclassoc-ea2ab2b7", "NetworkAclId": "acl-06ae18b1881404116", "SubnetId": "subnet-044ccee6422d8223a"}, {"NetworkAclAssociationId": "aclassoc-09a3197fe0c7a5aa0", "NetworkAclId": "acl-06ae18b1881404116", "SubnetId": "subnet-089b948e2e4b90a00"}, {"NetworkAclAssociationId": "aclassoc-9bd74cc6", "NetworkAclId": "acl-06ae18b1881404116", "SubnetId": "subnet-0406810f2609a424b"}, {"NetworkAclAssociationId": "aclassoc-0f99864d69d680dea", "NetworkAclId": "acl-06ae18b1881404116", "SubnetId": "subnet-0edd202ce59dbef4d"}, {"NetworkAclAssociationId": "aclassoc-60a13a3d", "NetworkAclId": "acl-06ae18b1881404116", "SubnetId": "subnet-086f43bd38909d79a"}, {"NetworkAclAssociationId": "aclassoc-08231adf397880095", "NetworkAclId": "acl-06ae18b1881404116", "SubnetId": "subnet-0a6fa98ed6d16df5b"}, {"NetworkAclAssociationId": "aclassoc-0ea95a19b875f2f39", "NetworkAclId": "acl-06ae18b1881404116", "SubnetId": "subnet-0e8f13a07600f4260"}, {"NetworkAclAssociationId": "aclassoc-0146174c87db18b21", "NetworkAclId": "acl-06ae18b1881404116", "SubnetId": "subnet-0c142e38f28d7e3f1"}, {"NetworkAclAssociationId": "aclassoc-004235f0a6e2872d9", "NetworkAclId": "acl-06ae18b1881404116", "SubnetId": "subnet-0a58f997a264a1268"}, {"NetworkAclAssociationId": "aclassoc-b2ea71ef", "NetworkAclId": "acl-06ae18b1881404116", "SubnetId": "subnet-0c1b61d0c3eb3b9c7"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-06ae18b1881404116", "Tags": [], "VpcId": "vpc-022f27d44556fea52", "OwnerId": "************"}}]