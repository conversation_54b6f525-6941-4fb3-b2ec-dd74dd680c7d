[{"account": "************", "timestamp": "2025-05-30T09:24:49Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-07794ab099435b9fb", "NetworkAclId": "acl-0c872106d110c448f", "SubnetId": "subnet-0a1f0d50daac01ae1"}, {"NetworkAclAssociationId": "aclassoc-07c4663e775de546a", "NetworkAclId": "acl-0c872106d110c448f", "SubnetId": "subnet-06f2d0d6902f407d6"}, {"NetworkAclAssociationId": "aclassoc-044dabae10d44be2d", "NetworkAclId": "acl-0c872106d110c448f", "SubnetId": "subnet-0db6f424170958c50"}, {"NetworkAclAssociationId": "aclassoc-09c252c1888f1a2f3", "NetworkAclId": "acl-0c872106d110c448f", "SubnetId": "subnet-03043f4ba53a62132"}, {"NetworkAclAssociationId": "aclassoc-0c8044c537ff59ed2", "NetworkAclId": "acl-0c872106d110c448f", "SubnetId": "subnet-050c793e53a709d2f"}, {"NetworkAclAssociationId": "aclassoc-05d6a571c50f1fce7", "NetworkAclId": "acl-0c872106d110c448f", "SubnetId": "subnet-0fed939196c5edb64"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-0c872106d110c448f", "Tags": [], "VpcId": "vpc-05b8ed45f5d0e80c3", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:24:58Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-08deeb3909b6bacfc", "NetworkAclId": "acl-0bdabd18568cf086d", "SubnetId": "subnet-0cadcf9a6bbb32cf0"}, {"NetworkAclAssociationId": "aclassoc-048cd0891071ed240", "NetworkAclId": "acl-0bdabd18568cf086d", "SubnetId": "subnet-09f991b99e8408cba"}, {"NetworkAclAssociationId": "aclassoc-b81640dd", "NetworkAclId": "acl-0bdabd18568cf086d", "SubnetId": "subnet-0b2b5b2c7e90aa3e4"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-0bdabd18568cf086d", "Tags": [], "VpcId": "vpc-05f575dc63dd7afb6", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:25:06Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-d92af1b0", "NetworkAclId": "acl-83f732ea", "SubnetId": "subnet-9fdd3ae4"}, {"NetworkAclAssociationId": "aclassoc-d82af1b1", "NetworkAclId": "acl-83f732ea", "SubnetId": "subnet-74dcd93e"}, {"NetworkAclAssociationId": "aclassoc-c62af1af", "NetworkAclId": "acl-83f732ea", "SubnetId": "subnet-56885f3f"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-83f732ea", "Tags": [], "VpcId": "vpc-20ca3049", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:25:14Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-8f6bd9ee", "NetworkAclId": "acl-01a03d0ac6a1830ab", "SubnetId": "subnet-01404509f03f9486c"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-01a03d0ac6a1830ab", "Tags": [], "VpcId": "vpc-0f45e1021278cb2f2", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:25:22Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-9b7855fa", "NetworkAclId": "acl-0aff902c626c92d40", "SubnetId": "subnet-052f488240a526544"}, {"NetworkAclAssociationId": "aclassoc-3a74595b", "NetworkAclId": "acl-0aff902c626c92d40", "SubnetId": "subnet-063a90401c2c485cd"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-0aff902c626c92d40", "Tags": [], "VpcId": "vpc-04d1133d5c8a810f4", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:25:31Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-381a0814", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-065ef71f4eb55a9aa"}, {"NetworkAclAssociationId": "aclassoc-0addedd1f54d3a97f", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-03edb6c368ea4bf71"}, {"NetworkAclAssociationId": "aclassoc-0b99d1151c3ee880f", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-0f6b308a87bbf57b3"}, {"NetworkAclAssociationId": "aclassoc-013227618d7266a7d", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-089f7936c56adc020"}, {"NetworkAclAssociationId": "aclassoc-36e6121f", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-097973401d5c4a277"}, {"NetworkAclAssociationId": "aclassoc-09f70bf231bb71313", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-074ada6974fa988d0"}, {"NetworkAclAssociationId": "aclassoc-0de8fa21", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-0016b597b53a16a7b"}, {"NetworkAclAssociationId": "aclassoc-0ce14f6fdf53954c0", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-0533436fd7dc7f983"}, {"NetworkAclAssociationId": "aclassoc-09b58e71803c8728d", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-0c4c1e1ff898ea22d"}, {"NetworkAclAssociationId": "aclassoc-0d8d150db03f7052b", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-06b5b14936361a370"}, {"NetworkAclAssociationId": "aclassoc-0f0ba24ae1e89c066", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-0226877bf7a32aea3"}, {"NetworkAclAssociationId": "aclassoc-07d7939e5d02fc9e7", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-09ee30ffd318020a1"}, {"NetworkAclAssociationId": "aclassoc-0e2d8f47db925b5d8", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-0dd511563d88d0aab"}, {"NetworkAclAssociationId": "aclassoc-2cde0a03", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-0697e4dc0a92f4b21"}, {"NetworkAclAssociationId": "aclassoc-017216b8fd9c1b4bd", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-0f33ad133f1052100"}, {"NetworkAclAssociationId": "aclassoc-0c9e7ccc507d60cdc", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-0be8b62dccda68453"}, {"NetworkAclAssociationId": "aclassoc-0fbbccbe0439571bf", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-0f099ec9c6c9c604f"}, {"NetworkAclAssociationId": "aclassoc-062e5cd5967f80267", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-024d8711bc093e7a1"}, {"NetworkAclAssociationId": "aclassoc-0ce2c0929f97c650c", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-055d460c409a99a3b"}, {"NetworkAclAssociationId": "aclassoc-03dc90e9b8d4518b2", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-0dd9c50ace77f31b4"}, {"NetworkAclAssociationId": "aclassoc-6144fe2d", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-5638411c"}, {"NetworkAclAssociationId": "aclassoc-0ab9db34384dc807e", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-048b3b71b7611c912"}, {"NetworkAclAssociationId": "aclassoc-0795c77eae51e8368", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-02ddcf5f622d803cb"}, {"NetworkAclAssociationId": "aclassoc-03ac9a5b2da89b44b", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-0a25d84691f7c9d69"}, {"NetworkAclAssociationId": "aclassoc-006d03f64ba6e6ede", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-052739068113d0bfd"}, {"NetworkAclAssociationId": "aclassoc-08846100f543f9020", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-044766db3398101ec"}, {"NetworkAclAssociationId": "aclassoc-07edd180d1857945e", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-04869aaf0889dd974"}, {"NetworkAclAssociationId": "aclassoc-19b3a335", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-00c6bb618a5f024d9"}, {"NetworkAclAssociationId": "aclassoc-c2248d8e", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-72bf145c"}, {"NetworkAclAssociationId": "aclassoc-0ceb6627de131f5ad", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-044faa971f46ef52b"}, {"NetworkAclAssociationId": "aclassoc-0c2a3d90696187f7f", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-0a0a73b694a8ec515"}, {"NetworkAclAssociationId": "aclassoc-08327d64d20d19ff7", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-07d3caa34566a265f"}, {"NetworkAclAssociationId": "aclassoc-0cded88eeffe794e8", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-0e0ab9e0c74ca6023"}, {"NetworkAclAssociationId": "aclassoc-0b2c54bb328be1b73", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-0b4e4f0830d3ae21c"}, {"NetworkAclAssociationId": "aclassoc-0745538031a01d2c1", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-0665ebe74f4f82deb"}, {"NetworkAclAssociationId": "aclassoc-04a77d90db25b82ce", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-04752dc8dafcae04e"}, {"NetworkAclAssociationId": "aclassoc-0712821e23f500b43", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-0bbf60b5a10478741"}, {"NetworkAclAssociationId": "aclassoc-06ebc24d8f5f9670c", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-0d315ba0480970a82"}, {"NetworkAclAssociationId": "aclassoc-743c8222", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-0d8dc127cf1b33739"}, {"NetworkAclAssociationId": "aclassoc-075262e1f47328518", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-00d3484fcb5a61b7a"}, {"NetworkAclAssociationId": "aclassoc-0b8a2d0dda16d770d", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-01f62f9c8803a7d81"}, {"NetworkAclAssociationId": "aclassoc-0427849a26e5cfffe", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-090e71ca38260c974"}, {"NetworkAclAssociationId": "aclassoc-060b982e6ecadc022", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-0fb73e8e6d664191b"}, {"NetworkAclAssociationId": "aclassoc-d2dcc0fe", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-0585a7b1ae9ddf484"}, {"NetworkAclAssociationId": "aclassoc-02ecf96725ba68e74", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-06b08cf97b2813d70"}, {"NetworkAclAssociationId": "aclassoc-97dc65c1", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-0d527a177d450f219"}, {"NetworkAclAssociationId": "aclassoc-0db67ca85c25387c2", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-0b2e6faa42887a923"}, {"NetworkAclAssociationId": "aclassoc-085a449d383c2f779", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-09e417973ac8eafc6"}, {"NetworkAclAssociationId": "aclassoc-385c4f14", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-02caceba7491ba4f0"}, {"NetworkAclAssociationId": "aclassoc-096236bdbf06f8980", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-07491ea4f535cc8b8"}, {"NetworkAclAssociationId": "aclassoc-0f9822f8e801abc30", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-0775f843dfe4bb263"}, {"NetworkAclAssociationId": "aclassoc-06e3548999f24393e", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-04cfc910c9c4c63ea"}, {"NetworkAclAssociationId": "aclassoc-2b21f504", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-020b9601727b54da1"}, {"NetworkAclAssociationId": "aclassoc-072f670b3d2ce838f", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-011cf0907efb04529"}, {"NetworkAclAssociationId": "aclassoc-0cb9a213d44090693", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-0f75e472b104971fe"}, {"NetworkAclAssociationId": "aclassoc-1edbc732", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-0703018ca188dca0b"}, {"NetworkAclAssociationId": "aclassoc-09dd47669b2bc02bc", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-07c43bddafdbeea5c"}, {"NetworkAclAssociationId": "aclassoc-d72c859b", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-743e9113"}, {"NetworkAclAssociationId": "aclassoc-0a410e129644f686d", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-0b31b11f022890591"}, {"NetworkAclAssociationId": "aclassoc-08b68c3916775e4e5", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-03e5a484320c0525b"}, {"NetworkAclAssociationId": "aclassoc-cb4f3be3", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-09373bf75c7197ade"}, {"NetworkAclAssociationId": "aclassoc-09d18d7b17132c779", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-0022e831351c12e17"}, {"NetworkAclAssociationId": "aclassoc-04bc866f6e775e3a6", "NetworkAclId": "acl-5d184327", "SubnetId": "subnet-09f06a368539dfea0"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-5d184327", "Tags": [], "VpcId": "vpc-7f748605", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:25:40Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-024461d7ddf74769f", "NetworkAclId": "acl-0143d69123a3c8446", "SubnetId": "subnet-0be2bbd2b3a5dedca"}, {"NetworkAclAssociationId": "aclassoc-002fc1d0f11081494", "NetworkAclId": "acl-0143d69123a3c8446", "SubnetId": "subnet-061506aeceda9ff22"}, {"NetworkAclAssociationId": "aclassoc-0012f0e77b8083dc2", "NetworkAclId": "acl-0143d69123a3c8446", "SubnetId": "subnet-0baaa70bbd97b334d"}, {"NetworkAclAssociationId": "aclassoc-041dc3adbfdaff387", "NetworkAclId": "acl-0143d69123a3c8446", "SubnetId": "subnet-04b5dbafbbdab04ae"}, {"NetworkAclAssociationId": "aclassoc-0fc880190474a8001", "NetworkAclId": "acl-0143d69123a3c8446", "SubnetId": "subnet-0142025c8f5aa3924"}, {"NetworkAclAssociationId": "aclassoc-0d9157daae73caddc", "NetworkAclId": "acl-0143d69123a3c8446", "SubnetId": "subnet-0c959f92f1bcf99b6"}, {"NetworkAclAssociationId": "aclassoc-0ea40877f970854b9", "NetworkAclId": "acl-0143d69123a3c8446", "SubnetId": "subnet-05e7000c8c917ffe3"}, {"NetworkAclAssociationId": "aclassoc-0d3cc565de1e0c999", "NetworkAclId": "acl-0143d69123a3c8446", "SubnetId": "subnet-0c4dad50b328f325a"}, {"NetworkAclAssociationId": "aclassoc-017c7992dda5bcc6c", "NetworkAclId": "acl-0143d69123a3c8446", "SubnetId": "subnet-0535ab66851c08af0"}, {"NetworkAclAssociationId": "aclassoc-029319060fa485554", "NetworkAclId": "acl-0143d69123a3c8446", "SubnetId": "subnet-07e99169dd121e91a"}, {"NetworkAclAssociationId": "aclassoc-07dfb2240e357aa0e", "NetworkAclId": "acl-0143d69123a3c8446", "SubnetId": "subnet-0220c62742e45830b"}, {"NetworkAclAssociationId": "aclassoc-028dd1b0bed18fc2c", "NetworkAclId": "acl-0143d69123a3c8446", "SubnetId": "subnet-08919576ece94ce5f"}, {"NetworkAclAssociationId": "aclassoc-0e18b469bc8083ef7", "NetworkAclId": "acl-0143d69123a3c8446", "SubnetId": "subnet-095e62f7b084065e5"}, {"NetworkAclAssociationId": "aclassoc-014ae7fb9a66cdcdc", "NetworkAclId": "acl-0143d69123a3c8446", "SubnetId": "subnet-00e9d40b46c558d9d"}, {"NetworkAclAssociationId": "aclassoc-0fb6050052cf1728a", "NetworkAclId": "acl-0143d69123a3c8446", "SubnetId": "subnet-092b2724235256742"}, {"NetworkAclAssociationId": "aclassoc-0db92fd8468d8c9ce", "NetworkAclId": "acl-0143d69123a3c8446", "SubnetId": "subnet-0a0d590651aa738c7"}, {"NetworkAclAssociationId": "aclassoc-0571a656ca7bcf75e", "NetworkAclId": "acl-0143d69123a3c8446", "SubnetId": "subnet-0470e8ebaced4f617"}, {"NetworkAclAssociationId": "aclassoc-0fd7a30bda82b9d5a", "NetworkAclId": "acl-0143d69123a3c8446", "SubnetId": "subnet-058ebc1767513b882"}, {"NetworkAclAssociationId": "aclassoc-02c81b07cedced4c5", "NetworkAclId": "acl-0143d69123a3c8446", "SubnetId": "subnet-0e6cefb8f9b16fa73"}, {"NetworkAclAssociationId": "aclassoc-09ec2b0d1b2b488d3", "NetworkAclId": "acl-0143d69123a3c8446", "SubnetId": "subnet-041263832554e2f66"}, {"NetworkAclAssociationId": "aclassoc-0c6def0792e651718", "NetworkAclId": "acl-0143d69123a3c8446", "SubnetId": "subnet-0899daac0087a1ee4"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-0143d69123a3c8446", "Tags": [], "VpcId": "vpc-0bf24b9c8f2fcac35", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:25:48Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-0a0fe91e8a7390b29", "NetworkAclId": "acl-07faf8e38f046fece", "SubnetId": "subnet-036dcd264fc425f70"}, {"NetworkAclAssociationId": "aclassoc-060000e90402dab8c", "NetworkAclId": "acl-07faf8e38f046fece", "SubnetId": "subnet-0121dba7be2908065"}, {"NetworkAclAssociationId": "aclassoc-0896c68a322155dab", "NetworkAclId": "acl-07faf8e38f046fece", "SubnetId": "subnet-0c27c3b9cef3443e5"}, {"NetworkAclAssociationId": "aclassoc-0bfda7543c9a6bdb4", "NetworkAclId": "acl-07faf8e38f046fece", "SubnetId": "subnet-0f2b0c22b2663452b"}, {"NetworkAclAssociationId": "aclassoc-0e559dee7940e6123", "NetworkAclId": "acl-07faf8e38f046fece", "SubnetId": "subnet-076f84acc20538a97"}, {"NetworkAclAssociationId": "aclassoc-c566f2e4", "NetworkAclId": "acl-07faf8e38f046fece", "SubnetId": "subnet-0cb7d03559b9fd92c"}, {"NetworkAclAssociationId": "aclassoc-00a68faf95da83eb4", "NetworkAclId": "acl-07faf8e38f046fece", "SubnetId": "subnet-07bdad071cfd7fc82"}, {"NetworkAclAssociationId": "aclassoc-9a5dc9bb", "NetworkAclId": "acl-07faf8e38f046fece", "SubnetId": "subnet-0a437962db40fca8b"}, {"NetworkAclAssociationId": "aclassoc-0bbefb6fef90f8bfb", "NetworkAclId": "acl-07faf8e38f046fece", "SubnetId": "subnet-01c468b3baedc15bc"}, {"NetworkAclAssociationId": "aclassoc-0bfd7ee07b4c536f7", "NetworkAclId": "acl-07faf8e38f046fece", "SubnetId": "subnet-0065febb41d9a2bbf"}, {"NetworkAclAssociationId": "aclassoc-0ba27e80531afe4c1", "NetworkAclId": "acl-07faf8e38f046fece", "SubnetId": "subnet-0fc67e9379ff4b28a"}, {"NetworkAclAssociationId": "aclassoc-21b72300", "NetworkAclId": "acl-07faf8e38f046fece", "SubnetId": "subnet-0bd9c38ce274928f2"}, {"NetworkAclAssociationId": "aclassoc-0de2a48fdcf0e7583", "NetworkAclId": "acl-07faf8e38f046fece", "SubnetId": "subnet-0e09ccbe55fde93c2"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-07faf8e38f046fece", "Tags": [], "VpcId": "vpc-0bbce2f96d237f872", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:25:55Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-0271f16707f6d7038", "NetworkAclId": "acl-0670ff30e7673e7d6", "SubnetId": "subnet-0e55863380446ba2c"}, {"NetworkAclAssociationId": "aclassoc-09c1aee2e76d40fda", "NetworkAclId": "acl-0670ff30e7673e7d6", "SubnetId": "subnet-01d55d2761c081755"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-0670ff30e7673e7d6", "Tags": [], "VpcId": "vpc-0d8210a114c83b535", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:26:06Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-01a45b79de516f82b", "NetworkAclId": "acl-00ff664d380debbc2", "SubnetId": "subnet-090807bdbb2368c46"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-00ff664d380debbc2", "Tags": [], "VpcId": "vpc-00629447d1b771f76", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:26:15Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-02af695a", "NetworkAclId": "acl-06754b20c73747ebf", "SubnetId": "subnet-01188266eb8f6644c"}, {"NetworkAclAssociationId": "aclassoc-dff83e87", "NetworkAclId": "acl-06754b20c73747ebf", "SubnetId": "subnet-04b31b1707a010a00"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-06754b20c73747ebf", "Tags": [], "VpcId": "vpc-04e5ae15ed6d6065d", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:26:24Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-05ddc22ee33b85338", "NetworkAclId": "acl-002d8081e1f0f22af", "SubnetId": "subnet-035d739c3eca20230"}, {"NetworkAclAssociationId": "aclassoc-03ca5e01e85117437", "NetworkAclId": "acl-002d8081e1f0f22af", "SubnetId": "subnet-0e73d7d6f42b436bb"}, {"NetworkAclAssociationId": "aclassoc-0a53745bcacdad585", "NetworkAclId": "acl-002d8081e1f0f22af", "SubnetId": "subnet-00d3f49c44d5f9de7"}, {"NetworkAclAssociationId": "aclassoc-07947723515db2e4d", "NetworkAclId": "acl-002d8081e1f0f22af", "SubnetId": "subnet-0c3d40e1b8b6200cb"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-002d8081e1f0f22af", "Tags": [], "VpcId": "vpc-00b9ad8c7941a70b3", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:26:33Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-402dfa3a", "NetworkAclId": "acl-60890a07", "SubnetId": "subnet-0972407f"}, {"NetworkAclAssociationId": "aclassoc-412dfa3b", "NetworkAclId": "acl-60890a07", "SubnetId": "subnet-0c460954"}, {"NetworkAclAssociationId": "aclassoc-462dfa3c", "NetworkAclId": "acl-60890a07", "SubnetId": "subnet-4eb49c73"}, {"NetworkAclAssociationId": "aclassoc-963a11e5", "NetworkAclId": "acl-60890a07", "SubnetId": "subnet-27ca552b"}, {"NetworkAclAssociationId": "aclassoc-b5577bcb", "NetworkAclId": "acl-60890a07", "SubnetId": "subnet-cbe87bae"}, {"NetworkAclAssociationId": "aclassoc-472dfa3d", "NetworkAclId": "acl-60890a07", "SubnetId": "subnet-1ba4e731"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-60890a07", "Tags": [], "VpcId": "vpc-8faa70e8", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:26:41Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-5d7ba676", "NetworkAclId": "acl-07862d1dfbe163009", "SubnetId": "subnet-0547c3065240ca36e"}, {"NetworkAclAssociationId": "aclassoc-12a07d39", "NetworkAclId": "acl-07862d1dfbe163009", "SubnetId": "subnet-0e9b3b4d53335d183"}, {"NetworkAclAssociationId": "aclassoc-d69e42fd", "NetworkAclId": "acl-07862d1dfbe163009", "SubnetId": "subnet-0fe3fd4e56c23bb83"}, {"NetworkAclAssociationId": "aclassoc-027ada87d113481a3", "NetworkAclId": "acl-07862d1dfbe163009", "SubnetId": "subnet-0acefa828c698d7db"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-07862d1dfbe163009", "Tags": [], "VpcId": "vpc-056af56b37e583a45", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:26:49Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-8b06b4f4", "NetworkAclId": "acl-f2841294", "SubnetId": "subnet-4da40671"}, {"NetworkAclAssociationId": "aclassoc-04d4ff1365b8e9000", "NetworkAclId": "acl-f2841294", "SubnetId": "subnet-0aa4d67a911545d93"}, {"NetworkAclAssociationId": "aclassoc-8e06b4f1", "NetworkAclId": "acl-f2841294", "SubnetId": "subnet-478f001c"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-f2841294", "Tags": [], "VpcId": "vpc-153df773", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:26:57Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-0cbde431ad69b90f8", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-033407f99d6421e3c"}, {"NetworkAclAssociationId": "aclassoc-0082c2c9b3b665422", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-0b89c5ef2899a23ff"}, {"NetworkAclAssociationId": "aclassoc-07a1d8614e21a0b15", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-0cafe8c47d4efb515"}, {"NetworkAclAssociationId": "aclassoc-052b279ccea280a27", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-056aa6bf7b6ecf371"}, {"NetworkAclAssociationId": "aclassoc-0701fa7f2d425f347", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-09b81d0315f749fbf"}, {"NetworkAclAssociationId": "aclassoc-0cab8ca9d570137b4", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-049958e943d6bb8e2"}, {"NetworkAclAssociationId": "aclassoc-0f07863da1936047e", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-0997863350a8c2912"}, {"NetworkAclAssociationId": "aclassoc-0602e9bf9144efe71", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-00f2d127ab1639bed"}, {"NetworkAclAssociationId": "aclassoc-0c7dc6a67a3e5f352", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-0f4b4cd230161329f"}, {"NetworkAclAssociationId": "aclassoc-0d8706013a20bedde", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-0a4206fc5f66916b1"}, {"NetworkAclAssociationId": "aclassoc-00c97bf64de59dbd4", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-0e65dd56a2397cb04"}, {"NetworkAclAssociationId": "aclassoc-05b05f4e6197ef7e8", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-0f5aea9be5a504994"}, {"NetworkAclAssociationId": "aclassoc-06648800a7bd26180", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-051a8a4ad5d4017e2"}, {"NetworkAclAssociationId": "aclassoc-05cc49e6b7f13f32c", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-03462c3a3e48586e9"}, {"NetworkAclAssociationId": "aclassoc-0136dc1123091b053", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-0de0c37f9ae855161"}, {"NetworkAclAssociationId": "aclassoc-0bdf7c2eff8bc3895", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-0c54c98b2543d46c3"}, {"NetworkAclAssociationId": "aclassoc-0d02d040dfd760ed4", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-04a710c8ecd8fd49d"}, {"NetworkAclAssociationId": "aclassoc-07d81159a4a3bf411", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-0570fa3268aeb9f8c"}, {"NetworkAclAssociationId": "aclassoc-068da7e6c7a1a078f", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-0ed47c257adc7a9cc"}, {"NetworkAclAssociationId": "aclassoc-0921c683862490bed", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-080c5819572998bef"}, {"NetworkAclAssociationId": "aclassoc-0e06189c8dc310ca1", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-05e65b09dc559e49f"}, {"NetworkAclAssociationId": "aclassoc-069fb93b8739f2816", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-056de5401b7f7b0f4"}, {"NetworkAclAssociationId": "aclassoc-0d87eb32b8f8d3a55", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-0c0b57e9a8f8b20e2"}, {"NetworkAclAssociationId": "aclassoc-0d7bd4f0324b6a22e", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-033f3ad8f498f824a"}, {"NetworkAclAssociationId": "aclassoc-06625e4244879521b", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-09e00e73c097cc7bb"}, {"NetworkAclAssociationId": "aclassoc-0c22ef21d053972b1", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-0fb502226c3acf4de"}, {"NetworkAclAssociationId": "aclassoc-073cc6f21fe1dba5c", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-0b54cf187e2e35ea8"}, {"NetworkAclAssociationId": "aclassoc-0e969b4420134b972", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-0ac532f8cc8d7e04e"}, {"NetworkAclAssociationId": "aclassoc-0141ae4863a2271f2", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-043794b61f91a029c"}, {"NetworkAclAssociationId": "aclassoc-078491dcdf16ce417", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-00e6e71936d4d3e75"}, {"NetworkAclAssociationId": "aclassoc-08bc6ebb1deea9764", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-0906b180141a1ab40"}, {"NetworkAclAssociationId": "aclassoc-09b8104a18f8b38cd", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-09e56347501166a0b"}, {"NetworkAclAssociationId": "aclassoc-0c25ca2c0865cd807", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-06b958154a40b8a71"}, {"NetworkAclAssociationId": "aclassoc-0d53597f045721b34", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-0c72a5b5b7838ded7"}, {"NetworkAclAssociationId": "aclassoc-013a5413925415810", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-09d1705fac377af38"}, {"NetworkAclAssociationId": "aclassoc-0b25b68565f0fc4ec", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-081a5bcac84d5c93e"}, {"NetworkAclAssociationId": "aclassoc-01b7caf1c2e9241b5", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-0f88f31403364fa91"}, {"NetworkAclAssociationId": "aclassoc-05ad7beb6b72ce342", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-0297bfa1670a81624"}, {"NetworkAclAssociationId": "aclassoc-06ef15dda8f6080b0", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-02a860837460dd354"}, {"NetworkAclAssociationId": "aclassoc-03d171ca3004a2903", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-072c8f561c84d1036"}, {"NetworkAclAssociationId": "aclassoc-0761063bae5052ae9", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-015d2141de3831cfa"}, {"NetworkAclAssociationId": "aclassoc-0eab32fa9a85ea36e", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-0604c3307c465a997"}, {"NetworkAclAssociationId": "aclassoc-07ac9b81ad57b6656", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-0da0d2fb38c631793"}, {"NetworkAclAssociationId": "aclassoc-0ee38245b77344bc3", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-032e7d0cbef62bb12"}, {"NetworkAclAssociationId": "aclassoc-0d0c94f6244697019", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-05b4cb50282037411"}, {"NetworkAclAssociationId": "aclassoc-01ece81a08f55be39", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-0bb2635e51c2f79e4"}, {"NetworkAclAssociationId": "aclassoc-0099ee5e366fb797c", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-00fcd2e2704888f62"}, {"NetworkAclAssociationId": "aclassoc-0ed9bb890eb14aec2", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-04647de17fb1cf4c5"}, {"NetworkAclAssociationId": "aclassoc-02bce032e877f6d03", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-075455a65235105ac"}, {"NetworkAclAssociationId": "aclassoc-089815ddd33d4c301", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-0cee6307003385078"}, {"NetworkAclAssociationId": "aclassoc-0acd863a31a3aff40", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-0c26b36e467c319de"}, {"NetworkAclAssociationId": "aclassoc-0680c22f758c09fca", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-002a9468e5f16047a"}, {"NetworkAclAssociationId": "aclassoc-06a817f567b1875a4", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-01ee1c941a5489934"}, {"NetworkAclAssociationId": "aclassoc-0299293ae176a0be9", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-0bc52fdde7423fe33"}, {"NetworkAclAssociationId": "aclassoc-05f895eeb5e168ca0", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-06cdc5bcc4bffb188"}, {"NetworkAclAssociationId": "aclassoc-0e6d3379ebbf45995", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-08c2ab807be8d1a97"}, {"NetworkAclAssociationId": "aclassoc-076426ad43b26d311", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-0e456c616ac7a993b"}, {"NetworkAclAssociationId": "aclassoc-0c0e35ed2867da992", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-075b24bd6c95f273f"}, {"NetworkAclAssociationId": "aclassoc-09875002413f97cdf", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-056862636f1dc0148"}, {"NetworkAclAssociationId": "aclassoc-03c0c678cbea3dd02", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-072d5ef42a5c67940"}, {"NetworkAclAssociationId": "aclassoc-0494e93eefb38b73d", "NetworkAclId": "acl-0061f141023709c28", "SubnetId": "subnet-0005081f6b0e4a7f5"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-0061f141023709c28", "Tags": [], "VpcId": "vpc-06d86353268b08dd2", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:27:05Z", "backup": {"Associations": [], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": false, "NetworkAclId": "acl-002fc3ed2def5f93b", "Tags": [{"Key": "Name", "Value": "keycloak"}], "VpcId": "vpc-00629447d1b771f76", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:27:13Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-0ea626792188c2c81", "NetworkAclId": "acl-04e13f144d32b41e3", "SubnetId": "subnet-0a9946ec027354717"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-04e13f144d32b41e3", "Tags": [], "VpcId": "vpc-0ecf02c7a3d63cd04", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:27:22Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-20fe4455", "NetworkAclId": "acl-ad4b46d4", "SubnetId": "subnet-cea6e594"}, {"NetworkAclAssociationId": "aclassoc-4edee53b", "NetworkAclId": "acl-ad4b46d4", "SubnetId": "subnet-e66045bc"}, {"NetworkAclAssociationId": "aclassoc-4505be30", "NetworkAclId": "acl-ad4b46d4", "SubnetId": "subnet-21d6da69"}, {"NetworkAclAssociationId": "aclassoc-7005be05", "NetworkAclId": "acl-ad4b46d4", "SubnetId": "subnet-dbe72bbf"}, {"NetworkAclAssociationId": "aclassoc-58605a2d", "NetworkAclId": "acl-ad4b46d4", "SubnetId": "subnet-c75474eb"}, {"NetworkAclAssociationId": "aclassoc-1dd5ee68", "NetworkAclId": "acl-ad4b46d4", "SubnetId": "subnet-b46cb0d0"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-ad4b46d4", "Tags": [], "VpcId": "vpc-a0db40d9", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:27:29Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-04b20e3202a367032", "NetworkAclId": "acl-023ef992284e77ba2", "SubnetId": "subnet-0b59b5f95536279ee"}, {"NetworkAclAssociationId": "aclassoc-0c6a3f61ddfb77f25", "NetworkAclId": "acl-023ef992284e77ba2", "SubnetId": "subnet-04946bc722df723e7"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-023ef992284e77ba2", "Tags": [], "VpcId": "vpc-06ee61c8d24e6f13d", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:27:38Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-01aa7cd3660589a12", "NetworkAclId": "acl-07e15ebfff719670b", "SubnetId": "subnet-0e693f501d1b9e7cc"}, {"NetworkAclAssociationId": "aclassoc-0add23b96b1ad9644", "NetworkAclId": "acl-07e15ebfff719670b", "SubnetId": "subnet-09378445115dd6cdf"}, {"NetworkAclAssociationId": "aclassoc-04b1441cd509fe60b", "NetworkAclId": "acl-07e15ebfff719670b", "SubnetId": "subnet-0a357a67cf6dcc07c"}, {"NetworkAclAssociationId": "aclassoc-08091d1447f495240", "NetworkAclId": "acl-07e15ebfff719670b", "SubnetId": "subnet-09863d4fde13fdeb3"}, {"NetworkAclAssociationId": "aclassoc-08d8479b593b00df2", "NetworkAclId": "acl-07e15ebfff719670b", "SubnetId": "subnet-0b3095cbd5884b796"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-07e15ebfff719670b", "Tags": [], "VpcId": "vpc-0b7f0e5d94dbe296c", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:27:45Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-045a7088ad5a82cde", "NetworkAclId": "acl-0c8d99cf949799304", "SubnetId": "subnet-0e206c0d67927bbd5"}, {"NetworkAclAssociationId": "aclassoc-085157a0f914817be", "NetworkAclId": "acl-0c8d99cf949799304", "SubnetId": "subnet-0c60307ec58b7636f"}, {"NetworkAclAssociationId": "aclassoc-00a96806a382dc069", "NetworkAclId": "acl-0c8d99cf949799304", "SubnetId": "subnet-00728487bc3ee26f5"}, {"NetworkAclAssociationId": "aclassoc-069b99cdff6455fcf", "NetworkAclId": "acl-0c8d99cf949799304", "SubnetId": "subnet-0b75d6d2fd4616b9e"}, {"NetworkAclAssociationId": "aclassoc-09565c5b9871384db", "NetworkAclId": "acl-0c8d99cf949799304", "SubnetId": "subnet-07424eac8d7bde03b"}, {"NetworkAclAssociationId": "aclassoc-0bf19f48b57755b5e", "NetworkAclId": "acl-0c8d99cf949799304", "SubnetId": "subnet-02f6532df82a58468"}, {"NetworkAclAssociationId": "aclassoc-09d30ff44aa382bbb", "NetworkAclId": "acl-0c8d99cf949799304", "SubnetId": "subnet-0891662c45394281b"}, {"NetworkAclAssociationId": "aclassoc-0ec8e225df69d0821", "NetworkAclId": "acl-0c8d99cf949799304", "SubnetId": "subnet-0c205a01fbbdb30e4"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-0c8d99cf949799304", "Tags": [], "VpcId": "vpc-07eb4ac32431e56f6", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:27:52Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-a4aa42df", "NetworkAclId": "acl-81962be6", "SubnetId": "subnet-95848ae3"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-81962be6", "Tags": [], "VpcId": "vpc-e7e63380", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:28:00Z", "backup": {"Associations": [], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-bdc295db", "Tags": [], "VpcId": "vpc-7d56d31b", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:28:07Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-39ab0c5e", "NetworkAclId": "acl-f0b39395", "SubnetId": "subnet-4a98f513"}, {"NetworkAclAssociationId": "aclassoc-d6226eaf", "NetworkAclId": "acl-f0b39395", "SubnetId": "subnet-e287fd86"}, {"NetworkAclAssociationId": "aclassoc-07ab0c60", "NetworkAclId": "acl-f0b39395", "SubnetId": "subnet-39085d4e"}, {"NetworkAclAssociationId": "aclassoc-38ab0c5f", "NetworkAclId": "acl-f0b39395", "SubnetId": "subnet-884077ed"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-f0b39395", "Tags": [], "VpcId": "vpc-f87c5c9d", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:28:16Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-0add38dc276648c25", "NetworkAclId": "acl-07a6e0738401413cc", "SubnetId": "subnet-0a6d90661235e5988"}, {"NetworkAclAssociationId": "aclassoc-0957cba5512ed8fde", "NetworkAclId": "acl-07a6e0738401413cc", "SubnetId": "subnet-01bde321de8eefff9"}, {"NetworkAclAssociationId": "aclassoc-0a37a6a807c1029dc", "NetworkAclId": "acl-07a6e0738401413cc", "SubnetId": "subnet-0c225bc6f3d34b79e"}, {"NetworkAclAssociationId": "aclassoc-07ec0e214fcad85a2", "NetworkAclId": "acl-07a6e0738401413cc", "SubnetId": "subnet-01c5201cb83ab2e55"}, {"NetworkAclAssociationId": "aclassoc-0e1191013eb2785b7", "NetworkAclId": "acl-07a6e0738401413cc", "SubnetId": "subnet-0a0f6fc39d8500ddd"}, {"NetworkAclAssociationId": "aclassoc-0da4ac905e7f12240", "NetworkAclId": "acl-07a6e0738401413cc", "SubnetId": "subnet-0cd01a20d6ce57173"}, {"NetworkAclAssociationId": "aclassoc-0df54537d5178770f", "NetworkAclId": "acl-07a6e0738401413cc", "SubnetId": "subnet-08f8a9b2f51922f1d"}, {"NetworkAclAssociationId": "aclassoc-057d512cdfad9aa82", "NetworkAclId": "acl-07a6e0738401413cc", "SubnetId": "subnet-0af33f09fc6c8d1f4"}, {"NetworkAclAssociationId": "aclassoc-08b5863028caeec73", "NetworkAclId": "acl-07a6e0738401413cc", "SubnetId": "subnet-0091749d5811fad28"}, {"NetworkAclAssociationId": "aclassoc-09c6c62856f5cda87", "NetworkAclId": "acl-07a6e0738401413cc", "SubnetId": "subnet-0085e97d2a88c90a1"}, {"NetworkAclAssociationId": "aclassoc-0f243843b4953208f", "NetworkAclId": "acl-07a6e0738401413cc", "SubnetId": "subnet-026d479212e885b77"}, {"NetworkAclAssociationId": "aclassoc-0ca31aead9102c707", "NetworkAclId": "acl-07a6e0738401413cc", "SubnetId": "subnet-0ce645594160b0bf2"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-07a6e0738401413cc", "Tags": [], "VpcId": "vpc-034f5d42cc309b752", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:28:26Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-00dfdf818d007bf67", "NetworkAclId": "acl-0628aa793cb31db5d", "SubnetId": "subnet-07438d3066133a6fc"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-0628aa793cb31db5d", "Tags": [], "VpcId": "vpc-0c9e79fee12081833", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:28:39Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-06d01fd1f74e4e2da", "NetworkAclId": "acl-0d9153df930954881", "SubnetId": "subnet-09003de184adb648a"}, {"NetworkAclAssociationId": "aclassoc-0333955d9a2809ff2", "NetworkAclId": "acl-0d9153df930954881", "SubnetId": "subnet-0c5d87f5ba982f9a4"}, {"NetworkAclAssociationId": "aclassoc-0b6bba187014e25eb", "NetworkAclId": "acl-0d9153df930954881", "SubnetId": "subnet-04a19003d6e9d0963"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-0d9153df930954881", "Tags": [], "VpcId": "vpc-05edc5ebdf28a323a", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:28:51Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-0d3fc7000b9048d0c", "NetworkAclId": "acl-0e2be52998303ac58", "SubnetId": "subnet-0376935dceace3790"}, {"NetworkAclAssociationId": "aclassoc-01b58d093614f50a3", "NetworkAclId": "acl-0e2be52998303ac58", "SubnetId": "subnet-0b7052b48bcff7af1"}, {"NetworkAclAssociationId": "aclassoc-0e64de4e2276aedc9", "NetworkAclId": "acl-0e2be52998303ac58", "SubnetId": "subnet-08384c972d071cbda"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "PortRange": {"From": 22, "To": 22}, "Protocol": "6", "RuleAction": "deny", "RuleNumber": 11}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "PortRange": {"From": 22, "To": 22}, "Protocol": "17", "RuleAction": "deny", "RuleNumber": 12}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "PortRange": {"From": 3389, "To": 3389}, "Protocol": "6", "RuleAction": "deny", "RuleNumber": 13}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "PortRange": {"From": 3389, "To": 3389}, "Protocol": "17", "RuleAction": "deny", "RuleNumber": 14}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-0e2be52998303ac58", "Tags": [], "VpcId": "vpc-03ad260398eb3a44d", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:29:01Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-023996e9dadaf670b", "NetworkAclId": "acl-0259c3960c14d2ef2", "SubnetId": "subnet-076ae0a813ebb2837"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "PortRange": {"From": 22, "To": 22}, "Protocol": "6", "RuleAction": "deny", "RuleNumber": 11}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "PortRange": {"From": 22, "To": 22}, "Protocol": "17", "RuleAction": "deny", "RuleNumber": 12}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "PortRange": {"From": 3389, "To": 3389}, "Protocol": "6", "RuleAction": "deny", "RuleNumber": 13}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "PortRange": {"From": 3389, "To": 3389}, "Protocol": "17", "RuleAction": "deny", "RuleNumber": 14}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-0259c3960c14d2ef2", "Tags": [], "VpcId": "vpc-029f039c776ee3530", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:29:10Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-********", "NetworkAclId": "acl-06d9b37b915ff3a79", "SubnetId": "subnet-045464242a5e4a14b"}, {"NetworkAclAssociationId": "aclassoc-547f6e11", "NetworkAclId": "acl-06d9b37b915ff3a79", "SubnetId": "subnet-00956ca7593c3a9a4"}, {"NetworkAclAssociationId": "aclassoc-475f4e02", "NetworkAclId": "acl-06d9b37b915ff3a79", "SubnetId": "subnet-0ff76764c89f4eba8"}, {"NetworkAclAssociationId": "aclassoc-44edfb01", "NetworkAclId": "acl-06d9b37b915ff3a79", "SubnetId": "subnet-0993ed6ed0affc808"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "PortRange": {"From": 22, "To": 22}, "Protocol": "6", "RuleAction": "deny", "RuleNumber": 11}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "PortRange": {"From": 22, "To": 22}, "Protocol": "17", "RuleAction": "deny", "RuleNumber": 12}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "PortRange": {"From": 3389, "To": 3389}, "Protocol": "6", "RuleAction": "deny", "RuleNumber": 13}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "PortRange": {"From": 3389, "To": 3389}, "Protocol": "17", "RuleAction": "deny", "RuleNumber": 14}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-06d9b37b915ff3a79", "Tags": [], "VpcId": "vpc-0d8fd8c4a0f53fa5c", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:29:18Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-917916df", "NetworkAclId": "acl-abcd5bd6", "SubnetId": "subnet-f07ddebd"}, {"NetworkAclAssociationId": "aclassoc-907916de", "NetworkAclId": "acl-abcd5bd6", "SubnetId": "subnet-987665ff"}, {"NetworkAclAssociationId": "aclassoc-ac7916e2", "NetworkAclId": "acl-abcd5bd6", "SubnetId": "subnet-10a2852e"}, {"NetworkAclAssociationId": "aclassoc-ad7916e3", "NetworkAclId": "acl-abcd5bd6", "SubnetId": "subnet-7296885c"}, {"NetworkAclAssociationId": "aclassoc-ae7916e0", "NetworkAclId": "acl-abcd5bd6", "SubnetId": "subnet-3ac5d966"}, {"NetworkAclAssociationId": "aclassoc-af7916e1", "NetworkAclId": "acl-abcd5bd6", "SubnetId": "subnet-b4fb2eba"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "PortRange": {"From": 22, "To": 22}, "Protocol": "6", "RuleAction": "deny", "RuleNumber": 11}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "PortRange": {"From": 22, "To": 22}, "Protocol": "17", "RuleAction": "deny", "RuleNumber": 12}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "PortRange": {"From": 3389, "To": 3389}, "Protocol": "6", "RuleAction": "deny", "RuleNumber": 13}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "PortRange": {"From": 3389, "To": 3389}, "Protocol": "17", "RuleAction": "deny", "RuleNumber": 14}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-abcd5bd6", "Tags": [], "VpcId": "vpc-e076599a", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:29:27Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-06ffd26e53cc309b4", "NetworkAclId": "acl-06ae18b1881404116", "SubnetId": "subnet-0fb728d3639e5ea70"}, {"NetworkAclAssociationId": "aclassoc-0ef9c7f03a698a36c", "NetworkAclId": "acl-06ae18b1881404116", "SubnetId": "subnet-0c2505ecc8badfc27"}, {"NetworkAclAssociationId": "aclassoc-ea2ab2b7", "NetworkAclId": "acl-06ae18b1881404116", "SubnetId": "subnet-044ccee6422d8223a"}, {"NetworkAclAssociationId": "aclassoc-09a3197fe0c7a5aa0", "NetworkAclId": "acl-06ae18b1881404116", "SubnetId": "subnet-089b948e2e4b90a00"}, {"NetworkAclAssociationId": "aclassoc-9bd74cc6", "NetworkAclId": "acl-06ae18b1881404116", "SubnetId": "subnet-0406810f2609a424b"}, {"NetworkAclAssociationId": "aclassoc-0f99864d69d680dea", "NetworkAclId": "acl-06ae18b1881404116", "SubnetId": "subnet-0edd202ce59dbef4d"}, {"NetworkAclAssociationId": "aclassoc-60a13a3d", "NetworkAclId": "acl-06ae18b1881404116", "SubnetId": "subnet-086f43bd38909d79a"}, {"NetworkAclAssociationId": "aclassoc-08231adf397880095", "NetworkAclId": "acl-06ae18b1881404116", "SubnetId": "subnet-0a6fa98ed6d16df5b"}, {"NetworkAclAssociationId": "aclassoc-0ea95a19b875f2f39", "NetworkAclId": "acl-06ae18b1881404116", "SubnetId": "subnet-0e8f13a07600f4260"}, {"NetworkAclAssociationId": "aclassoc-0146174c87db18b21", "NetworkAclId": "acl-06ae18b1881404116", "SubnetId": "subnet-0c142e38f28d7e3f1"}, {"NetworkAclAssociationId": "aclassoc-004235f0a6e2872d9", "NetworkAclId": "acl-06ae18b1881404116", "SubnetId": "subnet-0a58f997a264a1268"}, {"NetworkAclAssociationId": "aclassoc-b2ea71ef", "NetworkAclId": "acl-06ae18b1881404116", "SubnetId": "subnet-0c1b61d0c3eb3b9c7"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "PortRange": {"From": 22, "To": 22}, "Protocol": "6", "RuleAction": "deny", "RuleNumber": 11}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "PortRange": {"From": 22, "To": 22}, "Protocol": "17", "RuleAction": "deny", "RuleNumber": 12}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "PortRange": {"From": 3389, "To": 3389}, "Protocol": "6", "RuleAction": "deny", "RuleNumber": 13}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "PortRange": {"From": 3389, "To": 3389}, "Protocol": "17", "RuleAction": "deny", "RuleNumber": 14}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-06ae18b1881404116", "Tags": [], "VpcId": "vpc-022f27d44556fea52", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:29:36Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-*****************", "NetworkAclId": "acl-06f63b4789b907920", "SubnetId": "subnet-0cb519a58a529d645"}, {"NetworkAclAssociationId": "aclassoc-000c524c50738bb04", "NetworkAclId": "acl-06f63b4789b907920", "SubnetId": "subnet-07b36835cc963f865"}, {"NetworkAclAssociationId": "aclassoc-0ea2a18bbeab0ab8a", "NetworkAclId": "acl-06f63b4789b907920", "SubnetId": "subnet-087135f8b17c3a6c0"}, {"NetworkAclAssociationId": "aclassoc-048d8f6157eb7c224", "NetworkAclId": "acl-06f63b4789b907920", "SubnetId": "subnet-0d376bf81c8aa9101"}, {"NetworkAclAssociationId": "aclassoc-0171f8b600cdb8533", "NetworkAclId": "acl-06f63b4789b907920", "SubnetId": "subnet-0abb2bce59ed13822"}, {"NetworkAclAssociationId": "aclassoc-04cff31d1ce390b0e", "NetworkAclId": "acl-06f63b4789b907920", "SubnetId": "subnet-05434396fe63410b7"}, {"NetworkAclAssociationId": "aclassoc-07e65814824abc5c2", "NetworkAclId": "acl-06f63b4789b907920", "SubnetId": "subnet-03d7b05ffc25ded21"}, {"NetworkAclAssociationId": "aclassoc-0e9da8a620d8b9d0c", "NetworkAclId": "acl-06f63b4789b907920", "SubnetId": "subnet-0bbdba6afae3679d4"}, {"NetworkAclAssociationId": "aclassoc-00d0430078719fa83", "NetworkAclId": "acl-06f63b4789b907920", "SubnetId": "subnet-0687b008704a82ab6"}, {"NetworkAclAssociationId": "aclassoc-0d6bc742beeaf6cda", "NetworkAclId": "acl-06f63b4789b907920", "SubnetId": "subnet-0b673020758d0e51b"}, {"NetworkAclAssociationId": "aclassoc-0dafdbc858374e7d3", "NetworkAclId": "acl-06f63b4789b907920", "SubnetId": "subnet-027620cc894888619"}, {"NetworkAclAssociationId": "aclassoc-009da1e25c8daaa85", "NetworkAclId": "acl-06f63b4789b907920", "SubnetId": "subnet-0986d0920edc6010a"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-06f63b4789b907920", "Tags": [], "VpcId": "vpc-04962cee9d12728f5", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:29:45Z", "backup": {"Associations": [], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-082dd653afe407b9c", "Tags": [], "VpcId": "vpc-087af987ae48b0bf4", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:29:54Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-05a3d765c56073b31", "NetworkAclId": "acl-0f28335d292273aa6", "SubnetId": "subnet-01f508a17ac98c237"}, {"NetworkAclAssociationId": "aclassoc-00771b24b9d9cad83", "NetworkAclId": "acl-0f28335d292273aa6", "SubnetId": "subnet-00b435a765fafe15f"}, {"NetworkAclAssociationId": "aclassoc-00fbd5a9279e5a015", "NetworkAclId": "acl-0f28335d292273aa6", "SubnetId": "subnet-0b0f788a89c55a961"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-0f28335d292273aa6", "Tags": [], "VpcId": "vpc-0756c3a882d423177", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:30:05Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-02bc6048184d6adf3", "NetworkAclId": "acl-0093665c64d29ff59", "SubnetId": "subnet-0891bd13f1af09d4b"}, {"NetworkAclAssociationId": "aclassoc-0357a698f1c32840f", "NetworkAclId": "acl-0093665c64d29ff59", "SubnetId": "subnet-0dec0ad1484e2c725"}, {"NetworkAclAssociationId": "aclassoc-0ba6393a9f81e64c2", "NetworkAclId": "acl-0093665c64d29ff59", "SubnetId": "subnet-08f90e21461e4c85c"}, {"NetworkAclAssociationId": "aclassoc-00c56227100db7f8d", "NetworkAclId": "acl-0093665c64d29ff59", "SubnetId": "subnet-0321fc82f843b18fd"}, {"NetworkAclAssociationId": "aclassoc-034582da3378b84d7", "NetworkAclId": "acl-0093665c64d29ff59", "SubnetId": "subnet-0bed781232bb069f1"}, {"NetworkAclAssociationId": "aclassoc-0a1bceb2b7ac761da", "NetworkAclId": "acl-0093665c64d29ff59", "SubnetId": "subnet-0406dd3633b69511a"}, {"NetworkAclAssociationId": "aclassoc-02ea04baaa5da63ae", "NetworkAclId": "acl-0093665c64d29ff59", "SubnetId": "subnet-071e8048e7c4500d5"}, {"NetworkAclAssociationId": "aclassoc-08c6acf13916ee8bf", "NetworkAclId": "acl-0093665c64d29ff59", "SubnetId": "subnet-04e4aee172c8f0ab3"}, {"NetworkAclAssociationId": "aclassoc-0b6c7cd7949244f60", "NetworkAclId": "acl-0093665c64d29ff59", "SubnetId": "subnet-0495d71add3292f8b"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-0093665c64d29ff59", "Tags": [], "VpcId": "vpc-033bf5076082b8166", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:30:16Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-0fb9610263c7c190c", "NetworkAclId": "acl-0a76a4b05bd5d1b49", "SubnetId": "subnet-02c74118345ab8e87"}, {"NetworkAclAssociationId": "aclassoc-0dd02f014f87cabbe", "NetworkAclId": "acl-0a76a4b05bd5d1b49", "SubnetId": "subnet-0c4bc892464dcbb76"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"Egress": true, "Ipv6CidrBlock": "::/0", "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 101}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"Egress": true, "Ipv6CidrBlock": "::/0", "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32768}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"Egress": false, "Ipv6CidrBlock": "::/0", "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 101}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"Egress": false, "Ipv6CidrBlock": "::/0", "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32768}], "IsDefault": true, "NetworkAclId": "acl-0a76a4b05bd5d1b49", "Tags": [], "VpcId": "vpc-07deb297025af98a4", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:30:25Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-01c2edc1924c75e5d", "NetworkAclId": "acl-0d6ccba1938694df9", "SubnetId": "subnet-09365776721ee2575"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-0d6ccba1938694df9", "Tags": [], "VpcId": "vpc-0e9ca2cd48df1efdc", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:30:35Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-056da9d68a1bbbbde", "NetworkAclId": "acl-030255425ade6ef64", "SubnetId": "subnet-0ecc19aa8376e11bd"}, {"NetworkAclAssociationId": "aclassoc-0bdcfb4b69ef93ec0", "NetworkAclId": "acl-030255425ade6ef64", "SubnetId": "subnet-0d516167fa3dd91e5"}, {"NetworkAclAssociationId": "aclassoc-05daaf3bca47d921f", "NetworkAclId": "acl-030255425ade6ef64", "SubnetId": "subnet-058da9cbd200eddba"}, {"NetworkAclAssociationId": "aclassoc-03e8271c812868fc5", "NetworkAclId": "acl-030255425ade6ef64", "SubnetId": "subnet-03dffa4b75b752719"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "PortRange": {"From": 22, "To": 22}, "Protocol": "6", "RuleAction": "deny", "RuleNumber": 11}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "PortRange": {"From": 22, "To": 22}, "Protocol": "17", "RuleAction": "deny", "RuleNumber": 12}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "PortRange": {"From": 3389, "To": 3389}, "Protocol": "6", "RuleAction": "deny", "RuleNumber": 13}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "PortRange": {"From": 3389, "To": 3389}, "Protocol": "17", "RuleAction": "deny", "RuleNumber": 14}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-030255425ade6ef64", "Tags": [], "VpcId": "vpc-028b7c85eb42b7d4c", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T09:30:44Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-07affbc9d57918cee", "NetworkAclId": "acl-078a7880edbb6a2ec", "SubnetId": "subnet-0811fe74f14399c5a"}, {"NetworkAclAssociationId": "aclassoc-03fc69e4649fdba15", "NetworkAclId": "acl-078a7880edbb6a2ec", "SubnetId": "subnet-096bc23a616f027c0"}, {"NetworkAclAssociationId": "aclassoc-010644c56d2fe1568", "NetworkAclId": "acl-078a7880edbb6a2ec", "SubnetId": "subnet-07a3cc3855ff74bd0"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "PortRange": {"From": 22, "To": 22}, "Protocol": "6", "RuleAction": "deny", "RuleNumber": 11}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "PortRange": {"From": 22, "To": 22}, "Protocol": "17", "RuleAction": "deny", "RuleNumber": 12}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "PortRange": {"From": 3389, "To": 3389}, "Protocol": "6", "RuleAction": "deny", "RuleNumber": 13}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "PortRange": {"From": 3389, "To": 3389}, "Protocol": "17", "RuleAction": "deny", "RuleNumber": 14}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-078a7880edbb6a2ec", "Tags": [], "VpcId": "vpc-07ddd4972b4a266de", "OwnerId": "************"}}]