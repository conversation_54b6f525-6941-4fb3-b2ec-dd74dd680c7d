NACL Administrative Port Analysis Report
Generated: Wed May 28 21:36:31 CST 2025
==========================================

📋 Loading failing resources from: failing-resources.csv
🏢 Management Account ID: ************

📊 Found       17 unique accounts with NACL findings


🔍 Processing Account: ************
  Assuming cross-account role...
  ✅ Successfully assumed role
  No failing NACLs found for this account

🔍 Processing Account: ************
  Assuming cross-account role...
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************
  Assuming cross-account role...
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************
  Assuming cross-account role...
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************
  Assuming cross-account role...
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************
  Assuming cross-account role...
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************
  Assuming cross-account role...
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************
  Assuming cross-account role...
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************
  Assuming cross-account role...
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************
  Assuming cross-account role...
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************
  Assuming cross-account role...
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************
  Assuming cross-account role...
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************
  Assuming cross-account role...
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************
  Assuming cross-account role...
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************
  Assuming cross-account role...
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************
  Assuming cross-account role...
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************
  Assuming cross-account role...
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

==========================================
ANALYSIS COMPLETE
==========================================

📊 Summary Statistics:
  • Total accounts processed: 17
  • Successful accounts: 1
  • Failed accounts: 16
  • Total NACLs analyzed: 0
  • NACLs with admin ports: 0
  • Default NACLs: 0
  • Custom NACLs: 0

📁 Reports generated:
  • Detailed report: nacl-reports/nacl-analysis-report-********-213631.txt
  • Summary CSV: nacl-reports/nacl-analysis-summary-********-213631.csv
  • Statistics: nacl-reports/nacl-analysis-stats-********-213631.txt

⚠️  Some accounts failed to process. Check the detailed report for issues.
   Common causes: missing cross-account roles, suspended accounts
✅ Analysis complete! Review the reports before proceeding with remediation.
