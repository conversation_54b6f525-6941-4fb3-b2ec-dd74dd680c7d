NACL Administrative Port Analysis Report
Generated: Thu May 29 16:19:46 CST 2025
Management Account: ************
Accounts to process:       17
==========================================

📋 Loading failing resources from: failing-resources.csv
🏢 Management Account ID: ************

📊 Found       17 unique accounts with NACL findings


🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role
  No failing NACLs found for this account

🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ❌ Invalid JSON response from assume-role
    Response: 
Unable to locate credentials. You can configure credentials by running "aws configure".
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ❌ Invalid JSON response from assume-role
    Response: 
Unable to locate credentials. You can configure credentials by running "aws configure".
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ❌ Invalid JSON response from assume-role
    Response: 
Unable to locate credentials. You can configure credentials by running "aws configure".
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ❌ Invalid JSON response from assume-role
    Response: 
Unable to locate credentials. You can configure credentials by running "aws configure".
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ❌ Invalid JSON response from assume-role
    Response: 
Unable to locate credentials. You can configure credentials by running "aws configure".
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ❌ Invalid JSON response from assume-role
    Response: 
Unable to locate credentials. You can configure credentials by running "aws configure".
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ❌ Invalid JSON response from assume-role
    Response: 
Unable to locate credentials. You can configure credentials by running "aws configure".
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ❌ Invalid JSON response from assume-role
    Response: 
Unable to locate credentials. You can configure credentials by running "aws configure".
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ❌ Invalid JSON response from assume-role
    Response: 
Unable to locate credentials. You can configure credentials by running "aws configure".
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ❌ Invalid JSON response from assume-role
    Response: 
Unable to locate credentials. You can configure credentials by running "aws configure".
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ❌ Invalid JSON response from assume-role
    Response: 
Unable to locate credentials. You can configure credentials by running "aws configure".
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ❌ Invalid JSON response from assume-role
    Response: 
Unable to locate credentials. You can configure credentials by running "aws configure".
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ❌ Invalid JSON response from assume-role
    Response: 
Unable to locate credentials. You can configure credentials by running "aws configure".
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ❌ Invalid JSON response from assume-role
    Response: 
Unable to locate credentials. You can configure credentials by running "aws configure".
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ❌ Invalid JSON response from assume-role
    Response: 
Unable to locate credentials. You can configure credentials by running "aws configure".
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ❌ Invalid JSON response from assume-role
    Response: 
Unable to locate credentials. You can configure credentials by running "aws configure".
  ❌ Failed to assume role - skipping account
⚠️  Account ************ processing failed, continuing with next account...

==========================================
ANALYSIS COMPLETE
==========================================

📊 Summary Statistics:
  • Total accounts processed: 17
  • Successful accounts: 1
  • Failed accounts: 16
  • Total NACLs analyzed: 0
  • NACLs with admin ports: 0
  • Default NACLs: 0
  • Custom NACLs: 0

📁 Reports generated:
  • Detailed report: nacl-reports/nacl-analysis-report-********-161943.txt
  • Summary CSV: nacl-reports/nacl-analysis-summary-********-161943.csv
  • Statistics: nacl-reports/nacl-analysis-stats-********-161943.txt

⚠️  Some accounts failed to process. Check the detailed report for issues.
   Common causes: missing cross-account roles, suspended accounts
✅ Analysis complete! Review the reports before proceeding with remediation.
