NACL Administrative Port Analysis Report
Generated: Thu May 29 19:52:47 CST 2025
Management Account: ************
Accounts to process:        1
==========================================

📋 Loading failing resources from: failing-resources.csv
🏢 Management Account ID: ************

📊 Found        1 unique accounts with NACL findings


🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role
    Found NACL: acl-06d9b37b915ff3a79 in region us-east-1
    Found NACL: acl-abcd5bd6 in region us-east-1
    Found NACL: acl-06ae18b1881404116 in region us-east-1
    Region: us-east-1
      Analyzing NACL rules for acl-06d9b37b915ff3a79 in region us-east-1...
        🔍 Found SSH rule(s): 100
        🔍 Found RDP rule(s): 100
        ⚠️  NACL has admin port access from internet
      Analyzing NACL rules for acl-abcd5bd6 in region us-east-1...
        🔍 Found SSH rule(s): 100
        🔍 Found RDP rule(s): 100
        ⚠️  NACL has admin port access from internet
      Analyzing NACL rules for acl-06ae18b1881404116 in region us-east-1...
        🔍 Found SSH rule(s): 100
        🔍 Found RDP rule(s): 100
        ⚠️  NACL has admin port access from internet

==========================================
ANALYSIS COMPLETE
==========================================

📊 Summary Statistics:
  • Total accounts processed: 1
  • Successful accounts: 1
  • Failed accounts: 0
  • Total NACLs analyzed: 3
  • NACLs with admin ports: 3
  • Default NACLs: 0
  • Custom NACLs: 3

📁 Reports generated:
  • Detailed report: nacl-reports/nacl-analysis-report-********-195246.txt
  • Summary CSV: nacl-reports/nacl-analysis-summary-********-195246.csv
  • Statistics: nacl-reports/nacl-analysis-stats-********-195246.txt

✅ Analysis complete! Review the reports before proceeding with remediation.
