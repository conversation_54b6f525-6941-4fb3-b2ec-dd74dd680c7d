NACL Administrative Port Analysis Report
Generated: Thu May 29 19:34:01 CST 2025
Management Account: ************
Accounts to process:       17
==========================================

📋 Loading failing resources from: failing-resources.csv
🏢 Management Account ID: ************

📊 Found       17 unique accounts with NACL findings


🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role

==========================================
ANALYSIS COMPLETE
==========================================

📊 Summary Statistics:
  • Total accounts processed: 1
  • Successful accounts: 0
  • Failed accounts: 0
  • Total NACLs analyzed: 0
  • NACLs with admin ports: 0
  • Default NACLs: 0
  • Custom NACLs: 0

📁 Reports generated:
  • Detailed report: nacl-reports/nacl-analysis-report-********-193357.txt
  • Summary CSV: nacl-reports/nacl-analysis-summary-********-193357.csv
  • Statistics: nacl-reports/nacl-analysis-stats-********-193357.txt

✅ Analysis complete! Review the reports before proceeding with remediation.
