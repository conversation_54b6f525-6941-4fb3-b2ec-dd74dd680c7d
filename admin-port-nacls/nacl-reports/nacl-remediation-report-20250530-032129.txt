NACL Administrative Port Remediation Report
Generated: Fri May 30 03:21:29 CST 2025
Mode: DRY RUN
==========================================

🏢 Management Account ID: ************

📋 Processing       11 accounts


🔧 Processing Account: ************
  Assuming cross-account role...
  ✅ Successfully assumed role
    Region: us-east-2
      Analyzing and fixing NACL rules for acl-0c872106d110c448f...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs
      Analyzing and fixing NACL rules for acl-0bdabd18568cf086d...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs
      Analyzing and fixing NACL rules for acl-83f732ea...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs
      Analyzing and fixing NACL rules for acl-01a03d0ac6a1830ab...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs
      Analyzing and fixing NACL rules for acl-0aff902c626c92d40...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs
    Region: us-east-1
      Analyzing and fixing NACL rules for acl-5d184327...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs
      Analyzing and fixing NACL rules for acl-0143d69123a3c8446...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs
      Analyzing and fixing NACL rules for acl-07faf8e38f046fece...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs
      Analyzing and fixing NACL rules for acl-0670ff30e7673e7d6...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs
      Analyzing and fixing NACL rules for acl-00ff664d380debbc2...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs
      Analyzing and fixing NACL rules for acl-06754b20c73747ebf...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs
      Analyzing and fixing NACL rules for acl-002d8081e1f0f22af...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs
      Analyzing and fixing NACL rules for acl-60890a07...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs
      Analyzing and fixing NACL rules for acl-07862d1dfbe163009...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs
      Analyzing and fixing NACL rules for acl-f2841294...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs
      Analyzing and fixing NACL rules for acl-0061f141023709c28...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs
      Analyzing and fixing NACL rules for acl-002fc3ed2def5f93b...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs
      Analyzing and fixing NACL rules for acl-04e13f144d32b41e3...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs
      Analyzing and fixing NACL rules for acl-ad4b46d4...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs
      Analyzing and fixing NACL rules for acl-023ef992284e77ba2...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs
    Region: us-west-2
      Analyzing and fixing NACL rules for acl-07e15ebfff719670b...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs
      Analyzing and fixing NACL rules for acl-0c8d99cf949799304...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs
      Analyzing and fixing NACL rules for acl-81962be6...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs
      Analyzing and fixing NACL rules for acl-bdc295db...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs
      Analyzing and fixing NACL rules for acl-f0b39395...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs
    Region: eu-central-1
      Analyzing and fixing NACL rules for acl-07a6e0738401413cc...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs
      Analyzing and fixing NACL rules for acl-0628aa793cb31db5d...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs

🔧 Processing Account: ************
  Assuming cross-account role...
  ✅ Successfully assumed role
    Region: me-central-1
      Analyzing and fixing NACL rules for acl-0d9153df930954881...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs

🔧 Processing Account: ************
  Assuming cross-account role...
  ✅ Successfully assumed role
    Region: us-east-1
      Analyzing and fixing NACL rules for acl-0e2be52998303ac58...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs

🔧 Processing Account: ************
  Assuming cross-account role...
  ✅ Successfully assumed role
    Region: ca-central-1
      Analyzing and fixing NACL rules for acl-0259c3960c14d2ef2...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs

🔧 Processing Account: ************
  Assuming cross-account role...
  ✅ Successfully assumed role
    Region: us-east-1
      Analyzing and fixing NACL rules for acl-06d9b37b915ff3a79...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs
      Analyzing and fixing NACL rules for acl-abcd5bd6...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs
      Analyzing and fixing NACL rules for acl-06ae18b1881404116...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs

🔧 Processing Account: ************
  Assuming cross-account role...
  ✅ Successfully assumed role
    Region: us-east-1
      Analyzing and fixing NACL rules for acl-06f63b4789b907920...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs

🔧 Processing Account: ************
  Assuming cross-account role...
  ✅ Successfully assumed role
    Region: us-east-1
      Analyzing and fixing NACL rules for acl-082dd653afe407b9c...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs

🔧 Processing Account: ************
  Assuming cross-account role...
  ✅ Successfully assumed role
    Region: us-east-1
      Analyzing and fixing NACL rules for acl-0f28335d292273aa6...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs

🔧 Processing Account: ************
  Assuming cross-account role...
  ✅ Successfully assumed role
    Region: ap-southeast-2
      Analyzing and fixing NACL rules for acl-0093665c64d29ff59...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs

🔧 Processing Account: ************
  Assuming cross-account role...
  ✅ Successfully assumed role
    Region: us-east-1
      Analyzing and fixing NACL rules for acl-0a76a4b05bd5d1b49...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs
      Analyzing and fixing NACL rules for acl-0d6ccba1938694df9...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs

🔧 Processing Account: ************
  Assuming cross-account role...
  ✅ Successfully assumed role
    Region: us-east-1
      Analyzing and fixing NACL rules for acl-030255425ade6ef64...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs
      Analyzing and fixing NACL rules for acl-078a7880edbb6a2ec...
        🔍 Found problematic rule 100: Protocol:-1 Port:null-null Action:allow
Protocol:-1 Port:null-null Action:allow
        [DRY RUN] Would add DENY rules for SSH and RDP before rule 100
        [DRY RUN] Would add DENY rules for SSH/RDP on 0 NACLs

==========================================
REMEDIATION COMPLETE
==========================================

📊 Summary Statistics:
  • Total accounts processed: 11
  • Successful accounts: 11
  • Failed accounts: 0
  • Total NACLs processed: 41
  • NACLs fixed: 0
  • NACLs backed up: 0
  • DENY rules added: 0

📁 Reports generated:
  • Detailed report: nacl-reports/nacl-remediation-report-********-032129.txt
  • Summary CSV: nacl-reports/nacl-remediation-summary-********-032129.csv
  • Statistics: nacl-reports/nacl-remediation-stats-********-032129.txt

✅ Dry run complete! Review the reports and run in remediation mode when ready.

Next steps:
  1. Review the analysis results
  2. Test on a single account first: ./test-single-account-nacl.sh <account-id>
  3. Run in remediation mode: ./fix-admin-port-nacls.sh
