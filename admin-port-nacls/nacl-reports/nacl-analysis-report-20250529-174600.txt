NACL Administrative Port Analysis Report
Generated: Thu May 29 17:46:03 CST 2025
Management Account: ************
Accounts to process:       17
==========================================

📋 Loading failing resources from: failing-resources.csv
🏢 Management Account ID: ************

📊 Found       17 unique accounts with NACL findings


🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role
  No failing NACLs found for this account

🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role
  No failing NACLs found for this account

🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role
  No failing NACLs found for this account

🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role
  No failing NACLs found for this account

🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role
  No failing NACLs found for this account

🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role
  No failing NACLs found for this account

🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role
  No failing NACLs found for this account

🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role
  No failing NACLs found for this account

🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role
  No failing NACLs found for this account

🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role
  No failing NACLs found for this account

🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role
  No failing NACLs found for this account

🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role
  No failing NACLs found for this account

🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role
  No failing NACLs found for this account

🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role
  No failing NACLs found for this account

🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role
  No failing NACLs found for this account

🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role
  No failing NACLs found for this account

🔍 Processing Account: ************
  Assuming cross-account role...
    Attempting to assume role: arn:aws:iam::************:role/CrossAccountVPCManagementRole
    External ID: DrataCleanup2025
    ✅ Successfully assumed role and validated credentials
  ✅ Successfully assumed role
  No failing NACLs found for this account

==========================================
ANALYSIS COMPLETE
==========================================

📊 Summary Statistics:
  • Total accounts processed: 17
  • Successful accounts: 17
  • Failed accounts: 0
  • Total NACLs analyzed: 0
  • NACLs with admin ports: 0
  • Default NACLs: 0
  • Custom NACLs: 0

📁 Reports generated:
  • Detailed report: nacl-reports/nacl-analysis-report-********-174600.txt
  • Summary CSV: nacl-reports/nacl-analysis-summary-********-174600.csv
  • Statistics: nacl-reports/nacl-analysis-stats-********-174600.txt

✅ Analysis complete! Review the reports before proceeding with remediation.
