[{"account": "************", "timestamp": "2025-05-30T08:29:57Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-056da9d68a1bbbbde", "NetworkAclId": "acl-030255425ade6ef64", "SubnetId": "subnet-0ecc19aa8376e11bd"}, {"NetworkAclAssociationId": "aclassoc-0bdcfb4b69ef93ec0", "NetworkAclId": "acl-030255425ade6ef64", "SubnetId": "subnet-0d516167fa3dd91e5"}, {"NetworkAclAssociationId": "aclassoc-05daaf3bca47d921f", "NetworkAclId": "acl-030255425ade6ef64", "SubnetId": "subnet-058da9cbd200eddba"}, {"NetworkAclAssociationId": "aclassoc-03e8271c812868fc5", "NetworkAclId": "acl-030255425ade6ef64", "SubnetId": "subnet-03dffa4b75b752719"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-030255425ade6ef64", "Tags": [], "VpcId": "vpc-028b7c85eb42b7d4c", "OwnerId": "************"}}, {"account": "************", "timestamp": "2025-05-30T08:30:06Z", "backup": {"Associations": [{"NetworkAclAssociationId": "aclassoc-07affbc9d57918cee", "NetworkAclId": "acl-078a7880edbb6a2ec", "SubnetId": "subnet-0811fe74f14399c5a"}, {"NetworkAclAssociationId": "aclassoc-03fc69e4649fdba15", "NetworkAclId": "acl-078a7880edbb6a2ec", "SubnetId": "subnet-096bc23a616f027c0"}, {"NetworkAclAssociationId": "aclassoc-010644c56d2fe1568", "NetworkAclId": "acl-078a7880edbb6a2ec", "SubnetId": "subnet-07a3cc3855ff74bd0"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "allow", "RuleNumber": 100}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": true, "NetworkAclId": "acl-078a7880edbb6a2ec", "Tags": [], "VpcId": "vpc-07ddd4972b4a266de", "OwnerId": "************"}}]