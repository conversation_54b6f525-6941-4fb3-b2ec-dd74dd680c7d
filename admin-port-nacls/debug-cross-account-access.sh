#!/opt/homebrew/bin/bash

# Debug script to test cross-account access for NACL remediation
# This helps diagnose why accounts are failing

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo "=========================================="
echo "Cross-Account Access Debug Tool"
echo "=========================================="
echo ""

# Check if failing resources file exists
FAILING_RESOURCES_FILE="failing-resources.csv"
if [ ! -f "$FAILING_RESOURCES_FILE" ]; then
    echo "❌ Failing resources file not found: $FAILING_RESOURCES_FILE"
    exit 1
fi

# Get management account ID
echo -e "${BLUE}1. Checking current AWS identity...${NC}"
MANAGEMENT_ACCOUNT_ID=$(aws sts get-caller-identity --query 'Account' --output text 2>&1)
IDENTITY_EXIT_CODE=$?

if [ $IDENTITY_EXIT_CODE -ne 0 ]; then
    echo "❌ Failed to get current AWS identity"
    echo "Error: $MANAGEMENT_ACCOUNT_ID"
    echo ""
    echo "Possible issues:"
    echo "  - AWS CLI not configured"
    echo "  - AWS credentials expired"
    echo "  - No internet connection"
    exit 1
fi

echo "✅ Management Account ID: $MANAGEMENT_ACCOUNT_ID"
echo ""

# Check if jq is available
echo -e "${BLUE}2. Checking required tools...${NC}"
if ! command -v jq >/dev/null 2>&1; then
    echo "❌ jq is not installed"
    echo "Install with: brew install jq (macOS) or apt-get install jq (Linux)"
    exit 1
fi
echo "✅ jq is available"

# Check AWS CLI version
AWS_VERSION=$(aws --version 2>&1)
echo "✅ AWS CLI: $AWS_VERSION"
echo ""

# Get sample accounts from failing resources
echo -e "${BLUE}3. Extracting test accounts from failing resources...${NC}"
SAMPLE_ACCOUNTS=$(tail -n +2 "$FAILING_RESOURCES_FILE" | cut -d',' -f5 | sed 's/"//g' | sort -u | head -3)

if [ -z "$SAMPLE_ACCOUNTS" ]; then
    echo "❌ No accounts found in failing resources file"
    exit 1
fi

echo "Sample accounts to test:"
for account in $SAMPLE_ACCOUNTS; do
    echo "  - $account"
done
echo ""

# Function to test role assumption
test_assume_role() {
    local account_id=$1
    local role_name="CrossAccountVPCManagementRole"
    local external_id="DrataCleanup2025"
    local role_arn="arn:aws:iam::${account_id}:role/${role_name}"
    
    echo -e "${YELLOW}Testing account: $account_id${NC}"
    echo "  Role ARN: $role_arn"
    echo "  External ID: $external_id"
    
    # Test role assumption
    echo "  Attempting to assume role..."
    local assume_result=$(aws sts assume-role \
        --role-arn "$role_arn" \
        --role-session-name "NACLDebug-$(date +%s)" \
        --external-id "$external_id" \
        --duration-seconds 900 2>&1)
    
    local exit_code=$?
    
    if [ $exit_code -ne 0 ]; then
        echo "  ❌ Role assumption failed"
        echo "  Error: $assume_result"
        
        # Analyze the error
        if echo "$assume_result" | grep -q "AccessDenied"; then
            echo "  💡 Diagnosis: Access Denied"
            echo "     - The role may not exist"
            echo "     - The role may not trust the management account"
            echo "     - Check role deployment status"
        elif echo "$assume_result" | grep -q "InvalidUserID.NotFound"; then
            echo "  💡 Diagnosis: Role Not Found"
            echo "     - The role does not exist in this account"
            echo "     - Run: ../cross-account-roles/create-cross-account-roles.sh"
        elif echo "$assume_result" | grep -q "TokenRefreshRequired"; then
            echo "  💡 Diagnosis: Token Expired"
            echo "     - Your AWS session has expired"
            echo "     - Refresh your AWS credentials"
        else
            echo "  💡 Diagnosis: Unknown Error"
            echo "     - Check the error message above"
        fi
        echo ""
        return 1
    fi
    
    # Validate JSON response
    if ! echo "$assume_result" | jq -e '.Credentials' >/dev/null 2>&1; then
        echo "  ❌ Invalid JSON response"
        echo "  Response: $assume_result"
        echo ""
        return 1
    fi
    
    echo "  ✅ Role assumption successful"
    
    # Test the credentials
    echo "  Testing assumed role credentials..."
    local access_key=$(echo "$assume_result" | jq -r '.Credentials.AccessKeyId')
    local secret_key=$(echo "$assume_result" | jq -r '.Credentials.SecretAccessKey')
    local session_token=$(echo "$assume_result" | jq -r '.Credentials.SessionToken')
    
    # Test identity with assumed role
    local test_identity=$(AWS_ACCESS_KEY_ID="$access_key" \
                         AWS_SECRET_ACCESS_KEY="$secret_key" \
                         AWS_SESSION_TOKEN="$session_token" \
                         aws sts get-caller-identity --query 'Account' --output text 2>&1)
    
    local test_exit_code=$?
    
    if [ $test_exit_code -ne 0 ]; then
        echo "  ❌ Assumed role credentials don't work"
        echo "  Error: $test_identity"
        echo ""
        return 1
    fi
    
    if [ "$test_identity" != "$account_id" ]; then
        echo "  ❌ Wrong account in assumed role"
        echo "  Expected: $account_id, Got: $test_identity"
        echo ""
        return 1
    fi
    
    echo "  ✅ Assumed role credentials work correctly"
    
    # Test EC2 access
    echo "  Testing EC2 access in us-east-1..."
    local ec2_test=$(AWS_ACCESS_KEY_ID="$access_key" \
                    AWS_SECRET_ACCESS_KEY="$secret_key" \
                    AWS_SESSION_TOKEN="$session_token" \
                    aws ec2 describe-regions --region us-east-1 --query 'Regions[0].RegionName' --output text 2>&1)
    
    local ec2_exit_code=$?
    
    if [ $ec2_exit_code -ne 0 ]; then
        echo "  ❌ EC2 access failed"
        echo "  Error: $ec2_test"
        echo "  💡 The role may not have EC2 permissions"
        echo ""
        return 1
    fi
    
    echo "  ✅ EC2 access works"
    
    # Test NACL access with a sample NACL from this account
    local sample_nacl=$(grep ",$account_id," "$FAILING_RESOURCES_FILE" | head -1 | cut -d',' -f3 | sed 's/"//g')
    if [ -n "$sample_nacl" ]; then
        local region=$(echo "$sample_nacl" | cut -d':' -f4)
        local nacl_id=$(echo "$sample_nacl" | cut -d'/' -f2)
        
        echo "  Testing NACL access: $nacl_id in $region..."
        local nacl_test=$(AWS_ACCESS_KEY_ID="$access_key" \
                         AWS_SECRET_ACCESS_KEY="$secret_key" \
                         AWS_SESSION_TOKEN="$session_token" \
                         aws ec2 describe-network-acls \
                         --region "$region" \
                         --network-acl-ids "$nacl_id" \
                         --query 'NetworkAcls[0].NetworkAclId' \
                         --output text 2>&1)
        
        local nacl_exit_code=$?
        
        if [ $nacl_exit_code -ne 0 ]; then
            echo "  ❌ NACL access failed"
            echo "  Error: $nacl_test"
            
            if echo "$nacl_test" | grep -q "InvalidNetworkAclID.NotFound"; then
                echo "  💡 NACL no longer exists (may have been cleaned up)"
            else
                echo "  💡 Permission or other issue accessing NACL"
            fi
        else
            echo "  ✅ NACL access works: $nacl_test"
        fi
    fi
    
    echo "  ✅ Account $account_id: All tests passed"
    echo ""
    return 0
}

# Test each sample account
echo -e "${BLUE}4. Testing cross-account access...${NC}"
SUCCESSFUL_TESTS=0
FAILED_TESTS=0

for account in $SAMPLE_ACCOUNTS; do
    # Skip empty account IDs
    if [ -z "$account" ] || [ "$account" = "None" ]; then
        continue
    fi

    # Validate account ID format (12 digits)
    if [[ ! "$account" =~ ^[0-9]{12}$ ]]; then
        echo "⚠️  Skipping invalid account ID: '$account'"
        continue
    fi
    
    # Skip management account
    if [ "$account" = "$MANAGEMENT_ACCOUNT_ID" ]; then
        echo "⚠️  Skipping management account: $account"
        continue
    fi
    
    if test_assume_role "$account"; then
        SUCCESSFUL_TESTS=$((SUCCESSFUL_TESTS + 1))
    else
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
done

# Summary
echo "=========================================="
echo "Debug Summary"
echo "=========================================="
echo "Successful tests: $SUCCESSFUL_TESTS"
echo "Failed tests: $FAILED_TESTS"
echo ""

if [ $FAILED_TESTS -gt 0 ]; then
    echo -e "${RED}❌ Some accounts failed cross-account access${NC}"
    echo ""
    echo "Common solutions:"
    echo "1. Deploy cross-account roles:"
    echo "   cd ../cross-account-roles"
    echo "   ./create-cross-account-roles.sh"
    echo ""
    echo "2. Check role deployment status:"
    echo "   cd ../cross-account-roles"
    echo "   ./check-role-deployment.sh"
    echo ""
    echo "3. Verify your AWS credentials have Organizations access"
    echo ""
else
    echo -e "${GREEN}✅ All tested accounts have working cross-account access${NC}"
    echo ""
    echo "You can proceed with NACL analysis:"
    echo "  ./analyze-nacl-rules.sh"
fi
