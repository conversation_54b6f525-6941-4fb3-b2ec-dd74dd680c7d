AWSTemplateFormatVersion: '2010-09-09'
Description: 'AWS Config rule to monitor NACL rules for administrative port access from internet'

Parameters:
  ConfigRuleName:
    Type: String
    Default: 'nacl-no-admin-ports-from-internet'
    Description: 'Name for the AWS Config rule'
  
  ConfigDeliveryChannelName:
    Type: String
    Default: 'default'
    Description: 'Name of the Config delivery channel (must exist)'

Resources:
  NACLAdminPortConfigRule:
    Type: AWS::Config::ConfigRule
    Properties:
      ConfigRuleName: !Ref ConfigRuleName
      Description: 'Checks that Network ACLs do not allow unrestricted access to SSH (port 22) and RDP (port 3389) from the internet'
      Source:
        Owner: AWS
        SourceIdentifier: NACL_NO_UNRESTRICTED_SSH_RDP
      Scope:
        ComplianceResourceTypes:
          - AWS::EC2::NetworkAcl
      InputParameters: |
        {
          "blockedPort1": "22",
          "blockedPort2": "3389"
        }

  # Custom Lambda-based Config rule for more detailed checking
  NACLAdminPortCustomRule:
    Type: AWS::Config::ConfigRule
    Properties:
      ConfigRuleName: !Sub '${ConfigRuleName}-custom'
      Description: 'Custom rule to check NACL entries for admin ports from 0.0.0.0/0'
      Source:
        Owner: AWS
        SourceIdentifier: NACL_NO_UNRESTRICTED_PORTS
      Scope:
        ComplianceResourceTypes:
          - AWS::EC2::NetworkAcl
      InputParameters: |
        {
          "blockedPorts": "22,3389",
          "blockedCidr": "0.0.0.0/0"
        }

  # Remediation configuration for automatic fixing
  NACLRemediationConfiguration:
    Type: AWS::Config::RemediationConfiguration
    Properties:
      ConfigRuleName: !Ref NACLAdminPortConfigRule
      TargetType: SSM_DOCUMENT
      TargetId: AWSConfigRemediation-RemoveUnrestrictedSourceInNetworkAcl
      TargetVersion: '1'
      Parameters:
        AutomationAssumeRole:
          StaticValue: !GetAtt RemediationRole.Arn
        NetworkAclId:
          ResourceValue: RESOURCE_ID
        IpProtocol:
          StaticValue: tcp
        RuleAction:
          StaticValue: allow
        CidrBlock:
          StaticValue: 0.0.0.0/0
        PortRange:
          StaticValue: '22-22'
      Automatic: false
      MaximumAutomaticAttempts: 3

  # IAM role for remediation
  RemediationRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub 'ConfigRemediation-NACL-${AWS::Region}'
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: ssm.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/ConfigRole
      Policies:
        - PolicyName: NACLRemediationPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - ec2:DescribeNetworkAcls
                  - ec2:DeleteNetworkAclEntry
                  - ec2:CreateNetworkAclEntry
                  - ec2:ReplaceNetworkAclEntry
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                Resource: '*'

  # CloudWatch Event Rule for real-time monitoring
  NACLChangeEventRule:
    Type: AWS::Events::Rule
    Properties:
      Name: !Sub '${ConfigRuleName}-change-detection'
      Description: 'Detect NACL changes that might introduce admin port vulnerabilities'
      EventPattern:
        source:
          - aws.ec2
        detail-type:
          - AWS API Call via CloudTrail
        detail:
          eventSource:
            - ec2.amazonaws.com
          eventName:
            - CreateNetworkAclEntry
            - ReplaceNetworkAclEntry
            - DeleteNetworkAclEntry
      State: ENABLED
      Targets:
        - Arn: !GetAtt NACLChangeNotificationTopic.Arn
          Id: NACLChangeTarget

  # SNS Topic for notifications
  NACLChangeNotificationTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: !Sub '${ConfigRuleName}-notifications'
      DisplayName: 'NACL Admin Port Change Notifications'

  # SNS Topic Policy
  NACLChangeNotificationTopicPolicy:
    Type: AWS::SNS::TopicPolicy
    Properties:
      Topics:
        - !Ref NACLChangeNotificationTopic
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: events.amazonaws.com
            Action: sns:Publish
            Resource: !Ref NACLChangeNotificationTopic

Outputs:
  ConfigRuleName:
    Description: 'Name of the created Config rule'
    Value: !Ref NACLAdminPortConfigRule
    Export:
      Name: !Sub '${AWS::StackName}-ConfigRuleName'

  CustomConfigRuleName:
    Description: 'Name of the custom Config rule'
    Value: !Ref NACLAdminPortCustomRule
    Export:
      Name: !Sub '${AWS::StackName}-CustomConfigRuleName'

  RemediationRoleArn:
    Description: 'ARN of the remediation role'
    Value: !GetAtt RemediationRole.Arn
    Export:
      Name: !Sub '${AWS::StackName}-RemediationRoleArn'

  NotificationTopicArn:
    Description: 'ARN of the SNS topic for notifications'
    Value: !Ref NACLChangeNotificationTopic
    Export:
      Name: !Sub '${AWS::StackName}-NotificationTopicArn'
