#!/opt/homebrew/bin/bash

# Terraform-safe NACL remediation script
# This script only modifies NACLs that are NOT managed by Terraform
# It reads from failing-resources-enhanced.csv to check Terraform management status

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENHANCED_CSV="${ENHANCED_CSV:-failing-resources-enhanced.csv}"

echo "=========================================="
echo "Terraform-Safe NACL Remediation"
echo "=========================================="
echo ""

# Check if enhanced CSV exists
if [ ! -f "$ENHANCED_CSV" ]; then
    echo -e "${RED}❌ Enhanced CSV file not found: $ENHANCED_CSV${NC}"
    echo ""
    echo "Please run the tagging script first:"
    echo "  ./tag-nacl-resources.sh"
    echo ""
    exit 1
fi

# Check if Terraform_Managed column exists
if ! head -n 1 "$ENHANCED_CSV" | grep -q "Terraform_Managed"; then
    echo -e "${RED}❌ Enhanced CSV does not contain Terraform_Managed column${NC}"
    echo ""
    echo "Please run the tagging script first:"
    echo "  ./tag-nacl-resources.sh"
    echo ""
    exit 1
fi

echo -e "${BLUE}📋 Analyzing enhanced CSV file...${NC}"

# Count NACLs by Terraform management status
TOTAL_NACLS=$(tail -n +2 "$ENHANCED_CSV" | grep -v '^$' | wc -l)
TERRAFORM_MANAGED=$(tail -n +2 "$ENHANCED_CSV" | cut -d',' -f10 | grep -c "YES" 2>/dev/null || echo "0")
NOT_TERRAFORM_MANAGED=$(tail -n +2 "$ENHANCED_CSV" | cut -d',' -f10 | grep -c "NO" 2>/dev/null || echo "0")
ERROR_COUNT=$(tail -n +2 "$ENHANCED_CSV" | cut -d',' -f10 | grep -c "ERROR" 2>/dev/null || echo "0")
UNKNOWN_COUNT=$(tail -n +2 "$ENHANCED_CSV" | cut -d',' -f10 | grep -c "UNKNOWN" 2>/dev/null || echo "0")

# Ensure all variables are numeric
TOTAL_NACLS=${TOTAL_NACLS:-0}
TERRAFORM_MANAGED=${TERRAFORM_MANAGED:-0}
NOT_TERRAFORM_MANAGED=${NOT_TERRAFORM_MANAGED:-0}
ERROR_COUNT=${ERROR_COUNT:-0}
UNKNOWN_COUNT=${UNKNOWN_COUNT:-0}

echo ""
echo -e "${BLUE}📊 NACL Analysis Summary:${NC}"
echo "  • Total NACLs: $TOTAL_NACLS"
echo "  • Terraform managed: $TERRAFORM_MANAGED (will be SKIPPED)"
echo "  • NOT Terraform managed: $NOT_TERRAFORM_MANAGED (will be FIXED)"
echo "  • Errors: $ERROR_COUNT (will be SKIPPED)"
echo "  • Unknown: $UNKNOWN_COUNT (will be SKIPPED)"
echo ""

if [ $NOT_TERRAFORM_MANAGED -eq 0 ]; then
    echo -e "${YELLOW}⚠️  No NACLs found that are safe to modify${NC}"
    echo ""
    echo "All NACLs are either:"
    echo "  • Managed by Terraform (should be fixed via Terraform)"
    echo "  • Had errors during tag checking"
    echo "  • Have unknown Terraform status"
    echo ""
    echo "Consider:"
    echo "  1. Re-running the tagging script with fresh credentials"
    echo "  2. Fixing Terraform-managed NACLs through your Terraform code"
    echo "  3. Manually investigating NACLs with ERROR/UNKNOWN status"
    echo ""
    exit 0
fi

echo -e "${GREEN}✅ Found $NOT_TERRAFORM_MANAGED NACLs that are safe to modify${NC}"
echo ""

# Show which NACLs will be modified
echo -e "${BLUE}📋 NACLs that will be modified (NOT Terraform managed):${NC}"
tail -n +2 "$ENHANCED_CSV" | grep ",NO$" | while IFS= read -r line; do
    if [ -n "$line" ]; then
        account_id=$(echo "$line" | cut -d',' -f5 | sed 's/"//g')
        nacl_arn=$(echo "$line" | cut -d',' -f3 | sed 's/"//g')
        nacl_id=$(echo "$nacl_arn" | cut -d'/' -f2)
        region=$(echo "$nacl_arn" | cut -d':' -f4)
        account_name=$(echo "$line" | cut -d',' -f6 | sed 's/"//g')
        echo "  • $nacl_id ($region) in $account_id ($account_name)"
    fi
done

echo ""
echo -e "${YELLOW}⚠️  NACLs that will be SKIPPED (Terraform managed):${NC}"
if [ $TERRAFORM_MANAGED -gt 0 ]; then
    tail -n +2 "$ENHANCED_CSV" | grep ",YES$" | while IFS= read -r line; do
        if [ -n "$line" ]; then
            account_id=$(echo "$line" | cut -d',' -f5 | sed 's/"//g')
            nacl_arn=$(echo "$line" | cut -d',' -f3 | sed 's/"//g')
            nacl_id=$(echo "$nacl_arn" | cut -d'/' -f2)
            region=$(echo "$nacl_arn" | cut -d':' -f4)
            account_name=$(echo "$line" | cut -d',' -f6 | sed 's/"//g')
            echo "  • $nacl_id ($region) in $account_id ($account_name) - TERRAFORM MANAGED"
        fi
    done
else
    echo "  (None)"
fi

echo ""

# Mode selection
echo "Select mode:"
echo "1. Dry run (analyze only, no changes)"
echo "2. Fix NACL rules (add DENY rules for SSH/RDP)"
echo ""
read -p "Enter choice (1 or 2): " mode_choice

case $mode_choice in
    1)
        DRY_RUN=true
        echo -e "${BLUE}Running in DRY RUN mode - no changes will be made${NC}"
        ;;
    2)
        DRY_RUN=false
        echo -e "${YELLOW}Running in REMEDIATION mode - NACL rules will be modified${NC}"
        echo ""
        echo -e "${RED}⚠️  WARNING: This will add DENY rules for SSH/RDP from internet${NC}"
        echo -e "${GREEN}✅ Only NACLs NOT managed by Terraform will be modified${NC}"
        echo -e "${YELLOW}⚠️  Existing 'allow all' rules will be preserved (no service disruption)${NC}"
        echo -e "${BLUE}💾 Backups will be created automatically for rollback${NC}"
        echo ""
        read -p "Are you sure? Type 'FIX' to confirm: " confirm
        if [ "$confirm" != "FIX" ]; then
            echo "Operation cancelled."
            exit 0
        fi
        ;;
    *)
        echo "Invalid choice. Exiting."
        exit 1
        ;;
esac

echo ""
echo "=========================================="
echo "Starting Terraform-Safe NACL Remediation"
echo "=========================================="
echo ""

# Create a temporary file with only non-Terraform managed NACLs
TEMP_CSV="/tmp/nacl_terraform_safe.csv"
head -n 1 "$ENHANCED_CSV" > "$TEMP_CSV"
tail -n +2 "$ENHANCED_CSV" | grep ",NO$" >> "$TEMP_CSV"

# Count the filtered NACLs
FILTERED_COUNT=$(tail -n +2 "$TEMP_CSV" | wc -l)
echo -e "${GREEN}📋 Processing $FILTERED_COUNT Terraform-safe NACLs${NC}"
echo ""

# Now call the main remediation script with the filtered CSV
# We need to modify the main script to accept a custom CSV file
if [ "$DRY_RUN" = "true" ]; then
    echo "1" | CUSTOM_CSV="$TEMP_CSV" ./fix-admin-port-nacls.sh
else
    printf "2\nFIX\n" | CUSTOM_CSV="$TEMP_CSV" ./fix-admin-port-nacls.sh
fi

# Clean up
rm -f "$TEMP_CSV"

echo ""
echo "=========================================="
echo "Terraform-Safe Remediation Complete"
echo "=========================================="
echo ""

echo -e "${GREEN}✅ Terraform-safe remediation completed!${NC}"
echo ""
echo "Summary:"
echo "  • Processed: $FILTERED_COUNT NACLs (NOT Terraform managed)"
echo "  • Skipped: $TERRAFORM_MANAGED NACLs (Terraform managed)"
echo "  • Errors/Unknown: $((ERROR_COUNT + UNKNOWN_COUNT)) NACLs"
echo ""

if [ $TERRAFORM_MANAGED -gt 0 ]; then
    echo -e "${YELLOW}📋 Next steps for Terraform-managed NACLs:${NC}"
    echo "  1. Update your Terraform code to add DENY rules for SSH/RDP"
    echo "  2. Apply the Terraform changes"
    echo "  3. Re-run DRATA scan to verify compliance"
    echo ""
fi

echo -e "${BLUE}💡 Tip: Keep the enhanced CSV file for future reference${NC}"
