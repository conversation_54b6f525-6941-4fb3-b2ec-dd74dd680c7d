#!/opt/homebrew/bin/bash

# NACL Backup and Restore Utility
# This script helps manage NACL configuration backups and perform rollbacks

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

REPORTS_DIR="nacl-reports"

# Function to list available backups
list_backups() {
    echo "=========================================="
    echo "Available NACL Backups"
    echo "=========================================="
    
    if [ ! -d "$REPORTS_DIR" ]; then
        echo "❌ No reports directory found: $REPORTS_DIR"
        return 1
    fi
    
    local backup_files=$(ls "$REPORTS_DIR"/nacl-backups-*.json 2>/dev/null)
    
    if [ -z "$backup_files" ]; then
        echo "❌ No backup files found"
        echo "Run the remediation script first to create backups"
        return 1
    fi
    
    echo "Backup files:"
    for file in $backup_files; do
        local timestamp=$(basename "$file" | sed 's/nacl-backups-\(.*\)\.json/\1/')
        local count=$(jq length "$file" 2>/dev/null || echo "0")
        echo "  📁 $file"
        echo "     Timestamp: $timestamp"
        echo "     NACLs backed up: $count"
        echo ""
    done
}

# Function to show backup details
show_backup_details() {
    local backup_file="$1"
    
    if [ ! -f "$backup_file" ]; then
        echo "❌ Backup file not found: $backup_file"
        return 1
    fi
    
    echo "=========================================="
    echo "Backup Details: $(basename "$backup_file")"
    echo "=========================================="
    
    local count=$(jq length "$backup_file")
    echo "Total NACLs in backup: $count"
    echo ""
    
    echo "NACLs by account:"
    jq -r '.[] | "\(.account) \(.backup.VpcId) \(.backup.NetworkAclId)"' "$backup_file" | \
        awk '{print $1}' | sort | uniq -c | \
        awk '{printf "  Account %s: %d NACLs\n", $2, $1}'
    
    echo ""
    echo "Detailed list:"
    jq -r '.[] | "  \(.account) | \(.backup.VpcId) | \(.backup.NetworkAclId) | \(.timestamp)"' "$backup_file"
}

# Function to restore a specific NACL
restore_nacl() {
    local backup_file="$1"
    local account_id="$2"
    local nacl_id="$3"
    local dry_run="$4"
    
    if [ ! -f "$backup_file" ]; then
        echo "❌ Backup file not found: $backup_file"
        return 1
    fi
    
    # Find the NACL in the backup
    local nacl_backup=$(jq --arg account "$account_id" --arg nacl "$nacl_id" \
        '.[] | select(.account == $account and .backup.NetworkAclId == $nacl)' \
        "$backup_file")
    
    if [ -z "$nacl_backup" ] || [ "$nacl_backup" = "null" ]; then
        echo "❌ NACL $nacl_id not found in backup for account $account_id"
        return 1
    fi
    
    # Extract backup details
    local vpc_id=$(echo "$nacl_backup" | jq -r '.backup.VpcId')
    local region=$(echo "$nacl_backup" | jq -r '.backup.NetworkAclId' | cut -d'-' -f1)
    
    # Try to determine region from NACL ID format or VPC
    # This is a simplified approach - in practice you might need to check multiple regions
    local possible_regions=("us-east-1" "us-east-2" "us-west-1" "us-west-2" "eu-central-1" "ca-central-1" "me-central-1" "ap-southeast-2")
    
    echo "🔄 Restoring NACL $nacl_id in account $account_id..."
    
    # Assume cross-account role if not management account
    local management_account_id=$(aws sts get-caller-identity --query 'Account' --output text)
    
    if [ "$account_id" != "$management_account_id" ]; then
        echo "  Assuming cross-account role..."
        local role_arn="arn:aws:iam::${account_id}:role/CrossAccountVPCManagementRole"
        local external_id="DrataCleanup2025"
        
        local assume_result=$(aws sts assume-role \
            --role-arn "$role_arn" \
            --role-session-name "NACLRestore-$(date +%s)" \
            --external-id "$external_id" \
            --duration-seconds 3600 2>/dev/null || echo "ERROR")
        
        if [ "$assume_result" = "ERROR" ]; then
            echo "  ❌ Failed to assume role"
            return 1
        fi
        
        export AWS_ACCESS_KEY_ID=$(echo "$assume_result" | jq -r '.Credentials.AccessKeyId')
        export AWS_SECRET_ACCESS_KEY=$(echo "$assume_result" | jq -r '.Credentials.SecretAccessKey')
        export AWS_SESSION_TOKEN=$(echo "$assume_result" | jq -r '.Credentials.SessionToken')
    fi
    
    # Find the correct region by checking where the NACL exists
    local found_region=""
    for region in "${possible_regions[@]}"; do
        local nacl_exists=$(aws ec2 describe-network-acls \
            --region "$region" \
            --network-acl-ids "$nacl_id" \
            --query 'NetworkAcls[0].NetworkAclId' \
            --output text 2>/dev/null || echo "None")
        
        if [ "$nacl_exists" != "None" ] && [ "$nacl_exists" != "" ]; then
            found_region="$region"
            break
        fi
    done
    
    if [ -z "$found_region" ]; then
        echo "  ❌ Could not find NACL $nacl_id in any region"
        [ "$account_id" != "$management_account_id" ] && unset AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN
        return 1
    fi
    
    echo "  Found NACL in region: $found_region"
    
    # Get current NACL entries to compare
    local current_entries=$(aws ec2 describe-network-acls \
        --region "$found_region" \
        --network-acl-ids "$nacl_id" \
        --query 'NetworkAcls[0].Entries' \
        --output json 2>/dev/null || echo "[]")
    
    # Get backup entries
    local backup_entries=$(echo "$nacl_backup" | jq '.backup.Entries')
    
    echo "  Current entries: $(echo "$current_entries" | jq length)"
    echo "  Backup entries: $(echo "$backup_entries" | jq length)"
    
    if [ "$dry_run" = "true" ]; then
        echo "  [DRY RUN] Would restore NACL entries from backup"
        echo "  [DRY RUN] This would replace current rules with backup rules"
    else
        echo "  ⚠️  This will replace ALL current NACL rules with backup rules"
        read -p "  Continue? (y/N): " confirm
        
        if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
            echo "  Restore cancelled"
            [ "$account_id" != "$management_account_id" ] && unset AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN
            return 0
        fi
        
        echo "  🔧 Removing current entries..."
        # Remove all current entries (except default ones that can't be deleted)
        echo "$current_entries" | jq -r '.[] | select(.RuleNumber != 32767 and .RuleNumber != 100) | .RuleNumber' | \
        while read rule_number; do
            if [ -n "$rule_number" ]; then
                echo "    Removing rule $rule_number..."
                aws ec2 delete-network-acl-entry \
                    --region "$found_region" \
                    --network-acl-id "$nacl_id" \
                    --rule-number "$rule_number" 2>/dev/null || echo "    Failed to remove rule $rule_number"
            fi
        done
        
        echo "  🔧 Restoring backup entries..."
        # Add backup entries (except default ones)
        echo "$backup_entries" | jq -c '.[] | select(.RuleNumber != 32767 and .RuleNumber != 100)' | \
        while read entry; do
            local rule_number=$(echo "$entry" | jq -r '.RuleNumber')
            local protocol=$(echo "$entry" | jq -r '.Protocol')
            local rule_action=$(echo "$entry" | jq -r '.RuleAction')
            local cidr_block=$(echo "$entry" | jq -r '.CidrBlock // empty')
            local port_range=$(echo "$entry" | jq -r '.PortRange // empty')
            
            echo "    Restoring rule $rule_number..."
            
            # Build AWS CLI command based on entry type
            local cmd="aws ec2 create-network-acl-entry --region $found_region --network-acl-id $nacl_id --rule-number $rule_number --protocol $protocol --rule-action $rule_action"
            
            if [ "$cidr_block" != "" ] && [ "$cidr_block" != "null" ]; then
                cmd="$cmd --cidr-block $cidr_block"
            fi
            
            if [ "$port_range" != "" ] && [ "$port_range" != "null" ]; then
                local from_port=$(echo "$port_range" | jq -r '.From')
                local to_port=$(echo "$port_range" | jq -r '.To')
                cmd="$cmd --port-range From=$from_port,To=$to_port"
            fi
            
            eval "$cmd" 2>/dev/null || echo "    Failed to restore rule $rule_number"
        done
        
        echo "  ✅ NACL restore completed"
    fi
    
    # Reset credentials
    if [ "$account_id" != "$management_account_id" ]; then
        unset AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN
    fi
    
    return 0
}

# Function to restore all NACLs from a backup
restore_all_from_backup() {
    local backup_file="$1"
    local dry_run="$2"
    
    if [ ! -f "$backup_file" ]; then
        echo "❌ Backup file not found: $backup_file"
        return 1
    fi
    
    local count=$(jq length "$backup_file")
    echo "🔄 Restoring $count NACLs from backup..."
    
    if [ "$dry_run" = "false" ]; then
        echo "⚠️  This will restore ALL NACLs in the backup file"
        echo "⚠️  Current NACL rules will be replaced with backup rules"
        read -p "Continue? Type 'RESTORE' to confirm: " confirm
        
        if [ "$confirm" != "RESTORE" ]; then
            echo "Restore cancelled"
            return 0
        fi
    fi
    
    # Process each NACL in the backup
    jq -r '.[] | "\(.account) \(.backup.NetworkAclId)"' "$backup_file" | \
    while read account_id nacl_id; do
        echo ""
        restore_nacl "$backup_file" "$account_id" "$nacl_id" "$dry_run"
    done
}

# Main script logic
case "${1:-help}" in
    "list")
        list_backups
        ;;
    "show")
        if [ -z "$2" ]; then
            echo "Usage: $0 show <backup-file>"
            echo "Example: $0 show nacl-reports/nacl-backups-********-123456.json"
            exit 1
        fi
        show_backup_details "$2"
        ;;
    "restore")
        if [ $# -lt 4 ]; then
            echo "Usage: $0 restore <backup-file> <account-id> <nacl-id> [dry-run]"
            echo "Example: $0 restore nacl-reports/nacl-backups-********-123456.json ********9012 acl-********"
            echo "Add 'dry-run' as the last parameter to test without making changes"
            exit 1
        fi
        dry_run="false"
        if [ "$5" = "dry-run" ]; then
            dry_run="true"
        fi
        restore_nacl "$2" "$3" "$4" "$dry_run"
        ;;
    "restore-all")
        if [ -z "$2" ]; then
            echo "Usage: $0 restore-all <backup-file> [dry-run]"
            echo "Example: $0 restore-all nacl-reports/nacl-backups-********-123456.json"
            echo "Add 'dry-run' as the last parameter to test without making changes"
            exit 1
        fi
        dry_run="false"
        if [ "$3" = "dry-run" ]; then
            dry_run="true"
        fi
        restore_all_from_backup "$2" "$dry_run"
        ;;
    "help"|*)
        echo "=========================================="
        echo "NACL Backup and Restore Utility"
        echo "=========================================="
        echo ""
        echo "Commands:"
        echo "  $0 list                                    - List available backups"
        echo "  $0 show <backup-file>                      - Show backup details"
        echo "  $0 restore <backup-file> <account> <nacl>  - Restore single NACL"
        echo "  $0 restore-all <backup-file>               - Restore all NACLs from backup"
        echo ""
        echo "Examples:"
        echo "  $0 list"
        echo "  $0 show nacl-reports/nacl-backups-********-123456.json"
        echo "  $0 restore nacl-reports/nacl-backups-********-123456.json ********9012 acl-********"
        echo "  $0 restore-all nacl-reports/nacl-backups-********-123456.json dry-run"
        echo ""
        echo "Note: Add 'dry-run' to test restore operations without making changes"
        ;;
esac
