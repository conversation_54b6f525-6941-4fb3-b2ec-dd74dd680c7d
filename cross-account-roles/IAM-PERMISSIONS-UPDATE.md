# Cross-Account Role IAM Permissions Update

## Overview

The cross-account role template has been updated to support **DRATA Test-217: AWS IAM Group-Based Access Control** remediation. This document outlines the changes made and the rationale behind the new IAM permissions.

## Changes Made

### 1. Role Name Update
- **Previous**: `CrossAccountVPCManagementRole`
- **New**: `CrossAccountAuditRole`
- **Rationale**: Better reflects the expanded scope beyond VPC management

### 2. Description Update
- **Previous**: "Create cross-account role for VPC management, cleanup operations, and NACL security remediation"
- **New**: "Create cross-account role for VPC management, cleanup operations, NACL security remediation, and IAM group-based access control"

### 3. Tags Update
- **Purpose tag changed**: `CrossAccountVPCManagement` → `CrossAccountAuditAndRemediation`

### 4. Session Name Update
- **Previous**: `VPCCleanupSession`
- **New**: `DrataRemediationSession`

## New IAM Permissions Added

The following IAM permissions have been added to support group-based access control analysis and remediation:

### User Analysis Permissions
These permissions allow the scripts to analyze current user configurations:

```yaml
- 'iam:GetUser'                    # Get user details and metadata
- 'iam:ListUserPolicies'           # List inline policies attached to users
- 'iam:GetUserPolicy'              # Get inline policy documents for analysis
- 'iam:ListAttachedUserPolicies'   # List managed policies attached to users
- 'iam:GetGroupsForUser'           # Check current group memberships
```

### Group Management Permissions
These permissions enable creation and management of IAM groups:

```yaml
- 'iam:GetGroup'                   # Check if groups exist before creation
- 'iam:CreateGroup'                # Create new groups for user categories
- 'iam:ListGroups'                 # List existing groups for analysis
- 'iam:ListGroupPolicies'          # List group inline policies
- 'iam:GetGroupPolicy'             # Get group policy documents
- 'iam:ListAttachedGroupPolicies'  # List managed policies attached to groups
```

### Policy Migration Permissions
These permissions allow moving policies from users to groups:

```yaml
- 'iam:PutGroupPolicy'             # Create inline policies on groups
- 'iam:AttachGroupPolicy'          # Attach managed policies to groups
- 'iam:DetachUserPolicy'           # Remove managed policies from users
- 'iam:DeleteUserPolicy'           # Remove inline policies from users
```

### User-Group Membership Permissions
These permissions manage user membership in groups:

```yaml
- 'iam:AddUserToGroup'             # Add users to appropriate groups
- 'iam:RemoveUserFromGroup'        # Remove users from groups if needed
```

## Security Considerations

### Principle of Least Privilege
- All permissions are scoped to the minimum required for the remediation tasks
- No administrative permissions beyond what's needed for group-based access control
- Maintains existing security boundaries and external ID requirements

### Resource Scope
- All IAM permissions use `Resource: '*'` which is required for IAM operations
- This is standard practice for IAM management roles and follows AWS best practices
- The role is still constrained by the external ID and cross-account trust relationship

### Audit Trail
- All actions performed by this role are logged in CloudTrail
- The role name and session names clearly identify remediation activities
- Tags provide clear identification of the role's purpose

## Backward Compatibility

### Existing Functionality Preserved
- All existing VPC management permissions remain unchanged
- NACL security remediation permissions are preserved
- ECS, RDS, Lambda, and ELB permissions for VPC usage analysis remain intact

### Script Compatibility
- The role name change from `CrossAccountVPCManagementRole` to `CrossAccountAuditRole` requires updating scripts that reference the old name
- The IAM group-based access control scripts already reference `CrossAccountAuditRole`
- VPC and NACL scripts may need updates to use the new role name

## Deployment Considerations

### StackSet Update Required
To deploy these changes across your organization:

1. **Update the StackSet template** with the new CloudFormation template
2. **Deploy to all target accounts** in your organization
3. **Verify role creation** in each account
4. **Test cross-account access** with the new permissions

### Testing Recommendations
1. **Deploy to development accounts first** to validate functionality
2. **Test IAM group-based access control scripts** with the new permissions
3. **Verify existing VPC and NACL scripts** still work with the renamed role
4. **Validate CloudTrail logging** shows appropriate audit trail

## Usage with IAM Group-Based Access Control Scripts

The updated role supports the following remediation workflow:

### Analysis Phase
```bash
# The analyze-iam-users.sh script uses these permissions:
- iam:GetUser                    # Retrieve user details
- iam:ListUserPolicies           # Find inline policies
- iam:GetUserPolicy              # Get policy documents
- iam:ListAttachedUserPolicies   # Find attached managed policies
- iam:GetGroupsForUser           # Check current group memberships
```

### Remediation Phase
```bash
# The remediate-group-access.sh script uses these permissions:
- iam:GetGroup                   # Check if target groups exist
- iam:CreateGroup                # Create groups for user categories
- iam:PutGroupPolicy             # Move inline policies to groups
- iam:AttachGroupPolicy          # Move managed policies to groups
- iam:AddUserToGroup             # Add users to appropriate groups
- iam:DetachUserPolicy           # Remove policies from users
- iam:DeleteUserPolicy           # Remove inline policies from users
```

### Validation Phase
```bash
# The validate-group-access.sh script uses these permissions:
- iam:GetUser                    # Verify user configuration
- iam:ListUserPolicies           # Confirm no inline policies remain
- iam:ListAttachedUserPolicies   # Confirm no direct attachments remain
- iam:GetGroupsForUser           # Verify group memberships
```

## Next Steps

1. **Deploy the updated CloudFormation template** via StackSet
2. **Update any existing scripts** that reference the old role name
3. **Test the IAM group-based access control remediation** in development accounts
4. **Execute the full remediation workflow** across your organization
5. **Monitor CloudTrail logs** for audit compliance

## Support

For questions or issues with the updated permissions:
- Review the IAM group-based access control scripts in `../non-group-privileges/`
- Check CloudTrail logs for permission-related errors
- Validate the role deployment across all target accounts
- Test cross-account role assumption with the new role name
