#!/bin/bash

# Check what cross-account access you have configured
# This script helps diagnose your current AWS access setup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo "=========================================="
echo "Cross-Account Access Diagnostic Tool"
echo "=========================================="
echo ""

# Check current identity
echo -e "${BLUE}1. Checking current AWS identity...${NC}"
CURRENT_IDENTITY=$(aws sts get-caller-identity 2>/dev/null || echo "ERROR")

if [ "$CURRENT_IDENTITY" = "ERROR" ]; then
    echo -e "${RED}❌ AWS CLI not configured or no access${NC}"
    exit 1
fi

CURRENT_ACCOUNT=$(echo "$CURRENT_IDENTITY" | jq -r '.Account' 2>/dev/null || aws sts get-caller-identity --query 'Account' --output text)
CURRENT_ARN=$(echo "$CURRENT_IDENTITY" | jq -r '.Arn' 2>/dev/null || aws sts get-caller-identity --query 'Arn' --output text)
CURRENT_USER_ID=$(echo "$CURRENT_IDENTITY" | jq -r '.UserId' 2>/dev/null || aws sts get-caller-identity --query 'UserId' --output text)

echo "  Current Account: $CURRENT_ACCOUNT"
echo "  Current ARN: $CURRENT_ARN"
echo "  Current User ID: $CURRENT_USER_ID"

# Check if this is an Organizations management account
echo ""
echo -e "${BLUE}2. Checking AWS Organizations access...${NC}"

ORG_INFO=$(aws organizations describe-organization 2>/dev/null || echo "ERROR")

if [ "$ORG_INFO" = "ERROR" ]; then
    echo -e "${RED}❌ No AWS Organizations access${NC}"
    echo "  This might not be the management account, or Organizations is not enabled"
    IS_MANAGEMENT_ACCOUNT=false
else
    echo -e "${GREEN}✅ AWS Organizations access confirmed${NC}"
    
    MANAGEMENT_ACCOUNT=$(echo "$ORG_INFO" | jq -r '.Organization.MasterAccountId' 2>/dev/null || aws organizations describe-organization --query 'Organization.MasterAccountId' --output text)
    echo "  Management Account ID: $MANAGEMENT_ACCOUNT"
    
    if [ "$CURRENT_ACCOUNT" = "$MANAGEMENT_ACCOUNT" ]; then
        echo -e "${GREEN}✅ You are running from the management account${NC}"
        IS_MANAGEMENT_ACCOUNT=true
    else
        echo -e "${YELLOW}⚠️  You are NOT running from the management account${NC}"
        IS_MANAGEMENT_ACCOUNT=false
    fi
fi

# Get list of member accounts
echo ""
echo -e "${BLUE}3. Getting list of member accounts...${NC}"

if [ "$IS_MANAGEMENT_ACCOUNT" = "true" ]; then
    ACCOUNTS=$(aws organizations list-accounts --query 'Accounts[?Status==`ACTIVE`].Id' --output text 2>/dev/null || echo "ERROR")
    
    if [ "$ACCOUNTS" = "ERROR" ]; then
        echo -e "${RED}❌ Failed to list accounts${NC}"
        exit 1
    fi
    
    ACCOUNT_COUNT=$(echo "$ACCOUNTS" | wc -w)
    echo "  Found $ACCOUNT_COUNT active accounts"
    
    # Show first few accounts
    echo "  Sample accounts:"
    echo "$ACCOUNTS" | tr ' ' '\n' | head -5 | while read account; do
        echo "    - $account"
    done
    
    if [ "$ACCOUNT_COUNT" -gt 5 ]; then
        echo "    ... and $((ACCOUNT_COUNT - 5)) more"
    fi
else
    echo -e "${YELLOW}⚠️  Cannot list accounts (not management account)${NC}"
    ACCOUNTS=""
fi

# Test cross-account access
echo ""
echo -e "${BLUE}4. Testing cross-account access methods...${NC}"

# Common role names to test
COMMON_ROLE_NAMES=(
    "OrganizationAccountAccessRole"
    "AWSControlTowerExecution"
    "CrossAccountRole"
    "AdminRole"
    "PowerUserRole"
)

if [ -n "$ACCOUNTS" ]; then
    # Test with first member account (not management account)
    TEST_ACCOUNT=$(echo "$ACCOUNTS" | tr ' ' '\n' | grep -v "$CURRENT_ACCOUNT" | head -1)
    
    if [ -n "$TEST_ACCOUNT" ]; then
        echo "  Testing access to account: $TEST_ACCOUNT"
        
        for role_name in "${COMMON_ROLE_NAMES[@]}"; do
            echo -n "    Testing role: $role_name ... "
            
            ROLE_ARN="arn:aws:iam::${TEST_ACCOUNT}:role/${role_name}"
            
            # Try to assume the role
            ASSUME_RESULT=$(aws sts assume-role \
                --role-arn "$ROLE_ARN" \
                --role-session-name "CrossAccountTest" \
                --duration-seconds 900 2>/dev/null || echo "ERROR")
            
            if [ "$ASSUME_RESULT" = "ERROR" ]; then
                echo -e "${RED}❌${NC}"
            else
                echo -e "${GREEN}✅${NC}"
                
                # Test if we can actually use it to call EC2
                ACCESS_KEY=$(echo "$ASSUME_RESULT" | jq -r '.Credentials.AccessKeyId')
                SECRET_KEY=$(echo "$ASSUME_RESULT" | jq -r '.Credentials.SecretAccessKey')
                SESSION_TOKEN=$(echo "$ASSUME_RESULT" | jq -r '.Credentials.SessionToken')
                
                echo -n "      Testing EC2 access with this role ... "
                
                # Test EC2 access in us-east-1
                VPC_TEST=$(AWS_ACCESS_KEY_ID="$ACCESS_KEY" \
                          AWS_SECRET_ACCESS_KEY="$SECRET_KEY" \
                          AWS_SESSION_TOKEN="$SESSION_TOKEN" \
                          aws ec2 describe-vpcs \
                          --region us-east-1 \
                          --max-items 1 2>/dev/null || echo "ERROR")
                
                if [ "$VPC_TEST" = "ERROR" ]; then
                    echo -e "${RED}❌${NC}"
                else
                    echo -e "${GREEN}✅${NC}"
                    echo -e "${GREEN}      🎉 FOUND WORKING CROSS-ACCOUNT ACCESS!${NC}"
                    echo "      Role ARN: $ROLE_ARN"
                    
                    # Save this for later use
                    echo "WORKING_ROLE_NAME=$role_name" > .cross-account-config
                    echo "TEST_ACCOUNT=$TEST_ACCOUNT" >> .cross-account-config
                    break
                fi
            fi
        done
    else
        echo -e "${YELLOW}⚠️  No member accounts found to test${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  Cannot test cross-account access (no account list)${NC}"
fi

# Check for AWS Config or other service roles
echo ""
echo -e "${BLUE}5. Checking for existing service roles...${NC}"

if [ "$IS_MANAGEMENT_ACCOUNT" = "true" ]; then
    echo "  Checking for AWS Config service roles..."
    
    # Check if AWS Config is already set up
    CONFIG_RECORDERS=$(aws configservice describe-configuration-recorders --region us-east-1 2>/dev/null || echo "ERROR")
    
    if [ "$CONFIG_RECORDERS" != "ERROR" ]; then
        RECORDER_COUNT=$(echo "$CONFIG_RECORDERS" | jq '.ConfigurationRecorders | length' 2>/dev/null || echo "0")
        if [ "$RECORDER_COUNT" -gt 0 ]; then
            echo -e "${GREEN}✅ AWS Config is already set up in us-east-1${NC}"
        else
            echo "  No AWS Config recorders found in us-east-1"
        fi
    fi
fi

# Summary and recommendations
echo ""
echo "=========================================="
echo "Summary and Recommendations"
echo "=========================================="

if [ -f ".cross-account-config" ]; then
    source .cross-account-config
    echo -e "${GREEN}✅ Cross-account access is configured!${NC}"
    echo "  Working role: $WORKING_ROLE_NAME"
    echo "  You can use the VPC cleanup script directly"
    echo ""
    echo "  To use the role in scripts, use this pattern:"
    echo "    aws sts assume-role --role-arn arn:aws:iam::ACCOUNT:role/$WORKING_ROLE_NAME --role-session-name MySession"
else
    echo -e "${RED}❌ No working cross-account access found${NC}"
    echo ""
    echo "Options to set up cross-account access:"
    echo ""
    echo "1. If you have AWS Control Tower:"
    echo "   - Roles should already exist (AWSControlTowerExecution)"
    echo "   - Check if Control Tower is properly configured"
    echo ""
    echo "2. Create cross-account roles manually:"
    echo "   - Deploy OrganizationAccountAccessRole to all accounts"
    echo "   - Use AWS Organizations StackSets for this"
    echo ""
    echo "3. Use AWS Config approach (your original plan):"
    echo "   - More expensive but provides comprehensive access"
    echo "   - Good if you need ongoing compliance monitoring"
fi

echo ""
echo "Next steps:"
if [ -f ".cross-account-config" ]; then
    echo "1. Run the VPC cleanup script: ./find-delete-default-vpcs.sh"
    echo "2. Consider implementing the SCP to prevent future default VPCs"
else
    echo "1. Set up cross-account access (see options above)"
    echo "2. Or proceed with AWS Config approach if you need broader monitoring"
fi
