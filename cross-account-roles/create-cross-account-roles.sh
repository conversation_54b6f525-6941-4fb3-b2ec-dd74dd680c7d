#!/bin/bash

# Create cross-account roles using AWS Organizations StackSets
# This deploys IAM roles to all member accounts for VPC management

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
STACK_SET_NAME="CrossAccountVPCRoles"
ROOT_OU_ID="r-3g6q"

echo "=========================================="
echo "Cross-Account Role Deployment"
echo "=========================================="
echo ""

# Get current account ID (should be management account)
echo -e "${BLUE}1. Checking current AWS identity...${NC}"
MANAGEMENT_ACCOUNT_ID=$(aws sts get-caller-identity --query 'Account' --output text)
echo "  Management Account ID: $MANAGEMENT_ACCOUNT_ID"

# Verify we can access Organizations
echo ""
echo -e "${BLUE}2. Verifying AWS Organizations access...${NC}"
if ! aws organizations describe-organization >/dev/null 2>&1; then
    echo -e "${RED}❌ Cannot access AWS Organizations. Make sure you're running from the management account.${NC}"
    exit 1
fi

ORG_MANAGEMENT_ACCOUNT=$(aws organizations describe-organization --query 'Organization.MasterAccountId' --output text)
if [ "$MANAGEMENT_ACCOUNT_ID" != "$ORG_MANAGEMENT_ACCOUNT" ]; then
    echo -e "${RED}❌ Current account ($MANAGEMENT_ACCOUNT_ID) is not the management account ($ORG_MANAGEMENT_ACCOUNT)${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Running from management account${NC}"

# Get account count
ACCOUNT_COUNT=$(aws organizations list-accounts --query 'Accounts[?Status==`ACTIVE`] | length(@)' --output text)
echo "  Will deploy to $ACCOUNT_COUNT active accounts"

echo ""
echo -e "${BLUE}3. Creating StackSet...${NC}"

# Check if StackSet already exists
if aws cloudformation describe-stack-set --stack-set-name "$STACK_SET_NAME" >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  StackSet '$STACK_SET_NAME' already exists${NC}"
    read -p "Do you want to update it? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "Updating existing StackSet..."
        aws cloudformation update-stack-set \
            --stack-set-name "$STACK_SET_NAME" \
            --template-body file://cross-account-role-template.yaml \
            --parameters ParameterKey=ManagementAccountId,ParameterValue="$MANAGEMENT_ACCOUNT_ID" \
            --capabilities CAPABILITY_NAMED_IAM \
            --operation-preferences RegionConcurrencyType=PARALLEL,MaxConcurrentPercentage=100
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ StackSet updated successfully${NC}"
        else
            echo -e "${RED}❌ Failed to update StackSet${NC}"
            exit 1
        fi
    else
        echo "Skipping StackSet creation."
    fi
else
    echo "Creating new StackSet..."
    aws cloudformation create-stack-set \
        --stack-set-name "$STACK_SET_NAME" \
        --template-body file://cross-account-role-template.yaml \
        --parameters ParameterKey=ManagementAccountId,ParameterValue="$MANAGEMENT_ACCOUNT_ID" \
        --permission-model SERVICE_MANAGED \
        --auto-deployment Enabled=true,RetainStacksOnAccountRemoval=false \
        --capabilities CAPABILITY_NAMED_IAM
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ StackSet created successfully${NC}"
    else
        echo -e "${RED}❌ Failed to create StackSet${NC}"
        exit 1
    fi
fi

echo ""
echo -e "${BLUE}4. Deploying stack instances to all accounts...${NC}"

# Deploy to all accounts in the organization
echo "Deploying to all accounts under OU: $ROOT_OU_ID"
echo "This will deploy to $ACCOUNT_COUNT accounts in us-east-1 region"

read -p "Do you want to proceed? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Deployment cancelled."
    exit 0
fi

# Deploy stack instances (using us-east-1 as it's a global service)
aws cloudformation create-stack-instances \
    --stack-set-name "$STACK_SET_NAME" \
    --deployment-targets OrganizationalUnitIds="$ROOT_OU_ID" \
    --regions us-east-1 \
    --operation-preferences RegionConcurrencyType=PARALLEL,MaxConcurrentPercentage=100 \
    --operation-id "deploy-cross-account-roles-$(date +%Y%m%d-%H%M%S)"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Stack instance deployment initiated${NC}"
else
    echo -e "${RED}❌ Failed to deploy stack instances${NC}"
    exit 1
fi

echo ""
echo "=========================================="
echo "Deployment Summary"
echo "=========================================="
echo ""
echo -e "${GREEN}✅ Cross-account role deployment initiated successfully!${NC}"
echo ""
echo "What was created:"
echo "  • StackSet: $STACK_SET_NAME"
echo "  • Role Name: CrossAccountVPCManagementRole"
echo "  • External ID: VPCCleanup2024"
echo "  • Deployed to: All accounts under OU $ROOT_OU_ID"
echo ""
echo "Monitoring commands:"
echo "  1. Check deployment status:"
echo "     aws cloudformation list-stack-set-operations --stack-set-name $STACK_SET_NAME"
echo ""
echo "  2. List all stack instances:"
echo "     aws cloudformation list-stack-instances --stack-set-name $STACK_SET_NAME"
echo ""
echo "  3. Check for failed deployments:"
echo "     aws cloudformation list-stack-instances --stack-set-name $STACK_SET_NAME --query 'Summaries[?Status==\`FAILED\`]'"
echo ""
echo "Next steps:"
echo "  1. Wait for deployment to complete (usually 5-10 minutes)"
echo "  2. Run: ./check-cross-account-access.sh to verify access"
echo "  3. Run: ./find-delete-default-vpcs.sh to clean up VPCs"
echo ""
echo "Role ARN pattern for each account:"
echo "  arn:aws:iam::ACCOUNT-ID:role/CrossAccountVPCManagementRole"
echo ""
echo -e "${YELLOW}Note: The deployment is running in the background. Check the status using the commands above.${NC}"
