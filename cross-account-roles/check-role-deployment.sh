#!/bin/bash

# Check the deployment status of cross-account roles
# This script verifies that the roles were successfully deployed

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

STACK_SET_NAME="CrossAccountVPCRoles"
ROLE_NAME="CrossAccountVPCManagementRole"

echo "=========================================="
echo "Cross-Account Role Deployment Status"
echo "=========================================="
echo ""

# Check StackSet status
echo -e "${BLUE}1. Checking StackSet status...${NC}"

if ! aws cloudformation describe-stack-set --stack-set-name "$STACK_SET_NAME" >/dev/null 2>&1; then
    echo -e "${RED}❌ StackSet '$STACK_SET_NAME' not found${NC}"
    echo "Please run ./create-cross-account-roles.sh first"
    exit 1
fi

echo -e "${GREEN}✅ StackSet exists${NC}"

# Check latest operation
echo ""
echo -e "${BLUE}2. Checking latest deployment operation...${NC}"

LATEST_OPERATION=$(aws cloudformation list-stack-set-operations \
    --stack-set-name "$STACK_SET_NAME" \
    --max-items 1 \
    --query 'Summaries[0].OperationId' \
    --output text 2>/dev/null || echo "None")

if [ "$LATEST_OPERATION" != "None" ] && [ -n "$LATEST_OPERATION" ]; then
    OPERATION_STATUS=$(aws cloudformation describe-stack-set-operation \
        --stack-set-name "$STACK_SET_NAME" \
        --operation-id "$LATEST_OPERATION" \
        --query 'OperationStatus' \
        --output text)

    case $OPERATION_STATUS in
        "SUCCEEDED")
            echo -e "${GREEN}✅ Latest operation completed successfully${NC}"
            ;;
        "RUNNING")
            echo -e "${YELLOW}⏳ Operation still running...${NC}"
            ;;
        "FAILED")
            echo -e "${RED}❌ Latest operation failed${NC}"
            ;;
        "STOPPING"|"STOPPED")
            echo -e "${YELLOW}⚠️  Operation was stopped${NC}"
            ;;
    esac

    echo "  Operation ID: $LATEST_OPERATION"
    echo "  Status: $OPERATION_STATUS"
else
    echo -e "${YELLOW}⚠️  No operations found${NC}"
fi

# Check stack instances
echo ""
echo -e "${BLUE}3. Checking stack instances...${NC}"

INSTANCES=$(aws cloudformation list-stack-instances \
    --stack-set-name "$STACK_SET_NAME" \
    --query 'Summaries[*].{Account:Account,Status:Status}' \
    --output text 2>/dev/null || echo "")

if [ -z "$INSTANCES" ]; then
    echo -e "${RED}❌ No stack instances found${NC}"
    exit 1
fi

# Count instances by status
TOTAL_INSTANCES=$(echo "$INSTANCES" | wc -l)
CURRENT_INSTANCES=$(echo "$INSTANCES" | grep "CURRENT" | wc -l)
FAILED_INSTANCES=$(echo "$INSTANCES" | grep "FAILED" | wc -l)
OUTDATED_INSTANCES=$(echo "$INSTANCES" | grep "OUTDATED" | wc -l)

echo "  Total instances: $TOTAL_INSTANCES"
echo "  Current (successful): $CURRENT_INSTANCES"
echo "  Failed: $FAILED_INSTANCES"
echo "  Outdated: $OUTDATED_INSTANCES"

if [ "$FAILED_INSTANCES" -gt 0 ]; then
    echo ""
    echo -e "${RED}Failed instances:${NC}"
    echo "$INSTANCES" | grep "FAILED" | while read account status; do
        echo "  - Account: $account"
    done
fi

# Test role access on a few accounts
echo ""
echo -e "${BLUE}4. Testing role access...${NC}"

MANAGEMENT_ACCOUNT=$(aws sts get-caller-identity --query 'Account' --output text)
TEST_ACCOUNTS=$(echo "$INSTANCES" | grep "CURRENT" | head -3 | awk '{print $1}')

if [ -z "$TEST_ACCOUNTS" ]; then
    echo -e "${YELLOW}⚠️  No successful instances to test${NC}"
else
    echo "Testing role access on first 3 successful accounts..."

    for account in $TEST_ACCOUNTS; do
        if [ "$account" = "$MANAGEMENT_ACCOUNT" ]; then
            continue  # Skip management account
        fi

        echo -n "  Testing account $account ... "

        ROLE_ARN="arn:aws:iam::${account}:role/${ROLE_NAME}"

        # Try to assume the role
        ASSUME_RESULT=$(aws sts assume-role \
            --role-arn "$ROLE_ARN" \
            --role-session-name "RoleTest" \
            --external-id "DrataCleanup2025" \
            --duration-seconds 900 2>/dev/null || echo "ERROR")

        if [ "$ASSUME_RESULT" = "ERROR" ]; then
            echo -e "${RED}❌${NC}"
        else
            # Test if we can actually use it
            ACCESS_KEY=$(echo "$ASSUME_RESULT" | jq -r '.Credentials.AccessKeyId')
            SECRET_KEY=$(echo "$ASSUME_RESULT" | jq -r '.Credentials.SecretAccessKey')
            SESSION_TOKEN=$(echo "$ASSUME_RESULT" | jq -r '.Credentials.SessionToken')

            # Test VPC access
            VPC_TEST=$(AWS_ACCESS_KEY_ID="$ACCESS_KEY" \
                      AWS_SECRET_ACCESS_KEY="$SECRET_KEY" \
                      AWS_SESSION_TOKEN="$SESSION_TOKEN" \
                      aws ec2 describe-vpcs \
                      --region us-east-1 \
                      --max-items 1 2>/dev/null || echo "ERROR")

            if [ "$VPC_TEST" = "ERROR" ]; then
                echo -e "${YELLOW}⚠️  (role works but no VPC access)${NC}"
            else
                echo -e "${GREEN}✅${NC}"
            fi
        fi
    done
fi

# Summary
echo ""
echo "=========================================="
echo "Summary"
echo "=========================================="

if [ "$CURRENT_INSTANCES" -eq "$TOTAL_INSTANCES" ] && [ "$FAILED_INSTANCES" -eq 0 ]; then
    echo -e "${GREEN}🎉 All roles deployed successfully!${NC}"
    echo ""
    echo "You can now run the VPC cleanup script:"
    echo "  ./find-delete-default-vpcs.sh"
elif [ "$CURRENT_INSTANCES" -gt 0 ]; then
    echo -e "${YELLOW}⚠️  Partial deployment success${NC}"
    echo "  $CURRENT_INSTANCES out of $TOTAL_INSTANCES accounts have working roles"
    echo "  $FAILED_INSTANCES accounts failed"
    echo ""
    echo "You can proceed with VPC cleanup for successful accounts, or"
    echo "investigate and retry failed deployments."
else
    echo -e "${RED}❌ Deployment not ready${NC}"
    echo "Please wait for the deployment to complete or investigate failures."
fi

echo ""
echo "Useful commands:"
echo "  Check operation details:"
echo "    aws cloudformation describe-stack-set-operation --stack-set-name $STACK_SET_NAME --operation-id $LATEST_OPERATION"
echo ""
echo "  List all instances:"
echo "    aws cloudformation list-stack-instances --stack-set-name $STACK_SET_NAME"
