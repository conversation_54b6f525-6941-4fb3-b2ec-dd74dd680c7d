#!/bin/bash

# Debug script to test assume_role function

# Test account that we know works
TEST_ACCOUNT="************"

echo "Testing assume_role function with account: $TEST_ACCOUNT"
echo ""

# Function to assume cross-account role (with debug output)
assume_role_debug() {
    local account_id=$1
    local role_name="CrossAccountVPCManagementRole"
    local external_id="DrataCleanup2025"

    local role_arn="arn:aws:iam::${account_id}:role/${role_name}"
    
    echo "Role ARN: $role_arn"
    echo "External ID: $external_id"
    echo ""
    
    echo "Attempting to assume role..."
    
    # Try without error suppression first
    local assume_result=$(aws sts assume-role \
        --role-arn "$role_arn" \
        --role-session-name "VPCCleanup-$(date +%s)" \
        --external-id "$external_id" \
        --duration-seconds 3600 2>&1)
    
    local exit_code=$?
    
    echo "Exit code: $exit_code"
    echo ""
    
    if [ $exit_code -ne 0 ]; then
        echo "❌ Assume role failed with error:"
        echo "$assume_result"
        return 1
    fi
    
    echo "✅ Assume role succeeded!"
    echo ""
    
    # Test parsing with jq
    echo "Testing jq parsing..."
    
    local access_key=$(echo "$assume_result" | jq -r '.Credentials.AccessKeyId' 2>/dev/null)
    local secret_key=$(echo "$assume_result" | jq -r '.Credentials.SecretAccessKey' 2>/dev/null)
    local session_token=$(echo "$assume_result" | jq -r '.Credentials.SessionToken' 2>/dev/null)
    
    if [ "$access_key" = "null" ] || [ -z "$access_key" ]; then
        echo "❌ Failed to parse AccessKeyId with jq"
        echo "Raw output:"
        echo "$assume_result"
        return 1
    fi
    
    echo "✅ Successfully parsed credentials:"
    echo "  AccessKeyId: ${access_key:0:20}..."
    echo "  SecretAccessKey: ${secret_key:0:20}..."
    echo "  SessionToken: ${session_token:0:50}..."
    
    return 0
}

# Test the function
assume_role_debug "$TEST_ACCOUNT"

echo ""
echo "Now testing the exact same logic as in the main script..."
echo ""

# Test with the exact same logic as the main script
assume_role_exact() {
    local account_id=$1
    local role_name="CrossAccountVPCManagementRole"
    local external_id="DrataCleanup2025"

    local role_arn="arn:aws:iam::${account_id}:role/${role_name}"

    # Assume the role and export credentials (exact same as main script)
    local assume_result=$(aws sts assume-role \
        --role-arn "$role_arn" \
        --role-session-name "VPCCleanup-$(date +%s)" \
        --external-id "$external_id" \
        --duration-seconds 3600 2>/dev/null || echo "ERROR")

    echo "Result: '$assume_result'"
    
    if [ "$assume_result" = "ERROR" ]; then
        echo "❌ Function returned ERROR"
        return 1
    fi

    echo "✅ Function succeeded"
    
    # Test credential parsing
    export AWS_ACCESS_KEY_ID=$(echo "$assume_result" | jq -r '.Credentials.AccessKeyId')
    export AWS_SECRET_ACCESS_KEY=$(echo "$assume_result" | jq -r '.Credentials.SecretAccessKey')
    export AWS_SESSION_TOKEN=$(echo "$assume_result" | jq -r '.Credentials.SessionToken')
    
    echo "Exported credentials:"
    echo "  AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID:0:20}..."
    
    # Test if credentials work
    echo ""
    echo "Testing credentials with a simple AWS call..."
    if aws sts get-caller-identity >/dev/null 2>&1; then
        echo "✅ Credentials work!"
        aws sts get-caller-identity --query 'Account' --output text
    else
        echo "❌ Credentials don't work"
    fi
    
    # Reset credentials
    unset AWS_ACCESS_KEY_ID
    unset AWS_SECRET_ACCESS_KEY
    unset AWS_SESSION_TOKEN

    return 0
}

assume_role_exact "$TEST_ACCOUNT"
