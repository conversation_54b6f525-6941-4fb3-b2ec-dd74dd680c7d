AWSTemplateFormatVersion: '2010-09-09'
Description: 'Create cross-account role for VPC management, cleanup operations, and NACL security remediation'

Parameters:
  ManagementAccountId:
    Type: String
    Description: 'AWS Account ID of the management account that will assume this role'
    AllowedPattern: '[0-9]{12}'
    ConstraintDescription: 'Must be a valid 12-digit AWS Account ID'

Resources:
  CrossAccountVPCRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: CrossAccountVPCManagementRole
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              AWS: !Sub 'arn:aws:iam::${ManagementAccountId}:root'
            Action: 'sts:AssumeRole'
            Condition:
              StringEquals:
                'sts:ExternalId': 'DrataCleanup2025'
      ManagedPolicyArns:
        - 'arn:aws:iam::aws:policy/ReadOnlyAccess'
      Policies:
        - PolicyName: VPCManagementPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              # VPC and related resource permissions
              - Effect: Allow
                Action:
                  - 'ec2:DescribeVpcs'
                  - 'ec2:DescribeSubnets'
                  - 'ec2:DescribeInternetGateways'
                  - 'ec2:DescribeRouteTables'
                  - 'ec2:DescribeSecurityGroups'
                  - 'ec2:DescribeNetworkInterfaces'
                  - 'ec2:DescribeInstances'
                  - 'ec2:DescribeNatGateways'
                  - 'ec2:DescribeVpcEndpoints'
                  - 'ec2:DeleteVpc'
                  - 'ec2:DeleteSubnet'
                  - 'ec2:DeleteInternetGateway'
                  - 'ec2:DetachInternetGateway'
                  - 'ec2:DeleteRouteTable'
                  - 'ec2:DeleteSecurityGroup'
                  - 'ec2:DeleteNetworkInterface'
                Resource: '*'
              # NACL permissions for security remediation
              - Effect: Allow
                Action:
                  - 'ec2:DescribeNetworkAcls'
                  - 'ec2:CreateNetworkAclEntry'
                  - 'ec2:DeleteNetworkAclEntry'
                  - 'ec2:ReplaceNetworkAclEntry'
                Resource: '*'
              # RDS permissions to check for VPC usage
              - Effect: Allow
                Action:
                  - 'rds:DescribeDBInstances'
                  - 'rds:DescribeDBClusters'
                  - 'rds:DescribeDBSubnetGroups'
                Resource: '*'
              # Lambda permissions to check for VPC usage
              - Effect: Allow
                Action:
                  - 'lambda:ListFunctions'
                  - 'lambda:GetFunction'
                Resource: '*'
              # ELB permissions to check for VPC usage
              - Effect: Allow
                Action:
                  - 'elasticloadbalancing:DescribeLoadBalancers'
                  - 'elbv2:DescribeLoadBalancers'
                Resource: '*'
              # ECS permissions to check for VPC usage
              - Effect: Allow
                Action:
                  - 'ecs:ListClusters'
                  - 'ecs:DescribeClusters'
                  - 'ecs:ListServices'
                  - 'ecs:DescribeServices'
                Resource: '*'
      Tags:
        - Key: Purpose
          Value: CrossAccountVPCManagement
        - Key: CreatedBy
          Value: StackSet
        - Key: ManagedBy
          Value: OrganizationsStackSet

Outputs:
  RoleArn:
    Description: 'ARN of the created cross-account role'
    Value: !GetAtt CrossAccountVPCRole.Arn
    Export:
      Name: !Sub '${AWS::StackName}-CrossAccountRoleArn'

  RoleName:
    Description: 'Name of the created cross-account role'
    Value: !Ref CrossAccountVPCRole
    Export:
      Name: !Sub '${AWS::StackName}-CrossAccountRoleName'

  AssumeRoleCommand:
    Description: 'AWS CLI command to assume this role'
    Value: !Sub |
      aws sts assume-role \
        --role-arn ${CrossAccountVPCRole.Arn} \
        --role-session-name VPCCleanupSession \
        --external-id DrataCleanup2025
