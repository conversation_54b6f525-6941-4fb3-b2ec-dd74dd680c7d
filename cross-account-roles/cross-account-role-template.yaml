AWSTemplateFormatVersion: '2010-09-09'
Description: 'Create cross-account role for VPC management, cleanup operations, NACL security remediation, and IAM group-based access control'

Parameters:
  ManagementAccountId:
    Type: String
    Description: 'AWS Account ID of the management account that will assume this role'
    AllowedPattern: '[0-9]{12}'
    ConstraintDescription: 'Must be a valid 12-digit AWS Account ID'

Resources:
  CrossAccountAuditRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: CrossAccountAuditRole
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              AWS: !Sub 'arn:aws:iam::${ManagementAccountId}:root'
            Action: 'sts:AssumeRole'
            Condition:
              StringEquals:
                'sts:ExternalId': 'DrataCleanup2025'
      ManagedPolicyArns:
        - 'arn:aws:iam::aws:policy/ReadOnlyAccess'
      Policies:
        - PolicyName: VPCManagementPolicy
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              # VPC and related resource permissions
              - Effect: Allow
                Action:
                  - 'ec2:DescribeVpcs'
                  - 'ec2:DescribeSubnets'
                  - 'ec2:DescribeInternetGateways'
                  - 'ec2:DescribeRouteTables'
                  - 'ec2:DescribeSecurityGroups'
                  - 'ec2:DescribeNetworkInterfaces'
                  - 'ec2:DescribeInstances'
                  - 'ec2:DescribeNatGateways'
                  - 'ec2:DescribeVpcEndpoints'
                  - 'ec2:DeleteVpc'
                  - 'ec2:DeleteSubnet'
                  - 'ec2:DeleteInternetGateway'
                  - 'ec2:DetachInternetGateway'
                  - 'ec2:DeleteRouteTable'
                  - 'ec2:DeleteSecurityGroup'
                  - 'ec2:DeleteNetworkInterface'
                Resource: '*'
              # NACL permissions for security remediation
              - Effect: Allow
                Action:
                  - 'ec2:DescribeNetworkAcls'
                  - 'ec2:CreateNetworkAclEntry'
                  - 'ec2:DeleteNetworkAclEntry'
                  - 'ec2:ReplaceNetworkAclEntry'
                Resource: '*'
              # RDS permissions to check for VPC usage
              - Effect: Allow
                Action:
                  - 'rds:DescribeDBInstances'
                  - 'rds:DescribeDBClusters'
                  - 'rds:DescribeDBSubnetGroups'
                Resource: '*'
              # Lambda permissions to check for VPC usage
              - Effect: Allow
                Action:
                  - 'lambda:ListFunctions'
                  - 'lambda:GetFunction'
                Resource: '*'
              # ELB permissions to check for VPC usage
              - Effect: Allow
                Action:
                  - 'elasticloadbalancing:DescribeLoadBalancers'
                  - 'elbv2:DescribeLoadBalancers'
                Resource: '*'
              # ECS permissions to check for VPC usage
              - Effect: Allow
                Action:
                  - 'ecs:ListClusters'
                  - 'ecs:DescribeClusters'
                  - 'ecs:ListServices'
                  - 'ecs:DescribeServices'
                Resource: '*'
              # IAM permissions for group-based access control analysis and remediation
              # These permissions support DRATA Test-217 remediation for IAM group-based access control
              - Effect: Allow
                Action:
                  # User analysis permissions - required for analyzing current user configurations
                  - 'iam:GetUser'                    # Get user details and metadata
                  - 'iam:ListUserPolicies'           # List inline policies attached to users
                  - 'iam:GetUserPolicy'              # Get inline policy documents for analysis
                  - 'iam:ListAttachedUserPolicies'   # List managed policies attached to users
                  - 'iam:GetGroupsForUser'           # Check current group memberships
                  # Group management permissions - required for creating and managing IAM groups
                  - 'iam:GetGroup'                   # Check if groups exist before creation
                  - 'iam:CreateGroup'                # Create new groups for user categories
                  - 'iam:ListGroups'                 # List existing groups for analysis
                  - 'iam:ListGroupPolicies'          # List group inline policies
                  - 'iam:GetGroupPolicy'             # Get group policy documents
                  - 'iam:ListAttachedGroupPolicies'  # List managed policies attached to groups
                  # Policy migration permissions - required for moving policies from users to groups
                  - 'iam:PutGroupPolicy'             # Create inline policies on groups
                  - 'iam:AttachGroupPolicy'          # Attach managed policies to groups
                  - 'iam:DetachUserPolicy'           # Remove managed policies from users
                  - 'iam:DeleteUserPolicy'           # Remove inline policies from users
                  # User-group membership permissions - required for adding users to groups
                  - 'iam:AddUserToGroup'             # Add users to appropriate groups
                  - 'iam:RemoveUserFromGroup'        # Remove users from groups if needed
                Resource: '*'
      Tags:
        - Key: Purpose
          Value: CrossAccountAuditAndRemediation
        - Key: CreatedBy
          Value: StackSet
        - Key: ManagedBy
          Value: OrganizationsStackSet

Outputs:
  RoleArn:
    Description: 'ARN of the created cross-account audit role'
    Value: !GetAtt CrossAccountAuditRole.Arn
    Export:
      Name: !Sub '${AWS::StackName}-CrossAccountRoleArn'

  RoleName:
    Description: 'Name of the created cross-account audit role'
    Value: !Ref CrossAccountAuditRole
    Export:
      Name: !Sub '${AWS::StackName}-CrossAccountRoleName'

  AssumeRoleCommand:
    Description: 'AWS CLI command to assume this role'
    Value: !Sub |
      aws sts assume-role \
        --role-arn ${CrossAccountAuditRole.Arn} \
        --role-session-name DrataRemediationSession \
        --external-id DrataCleanup2025
