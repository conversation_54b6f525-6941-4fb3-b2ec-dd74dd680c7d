# Cross-Account Roles for DRATA Remediation

This directory contains CloudFormation templates and scripts for deploying cross-account IAM roles that support various DRATA security control remediations across your AWS Organization.

## Overview

The cross-account role provides secure, auditable access for automated remediation scripts to operate across multiple AWS accounts in your organization. The role supports:

- **VPC Management** - Unused VPC detection and cleanup
- **NACL Security Remediation** - Administrative port access control
- **IAM Group-Based Access Control** - User permission remediation
- **General Security Auditing** - ReadOnly access for compliance scanning

## Files

### Core Templates
- `cross-account-role-template.yaml` - CloudFormation template for the cross-account role
- `IAM-PERMISSIONS-UPDATE.md` - Detailed documentation of recent IAM permission updates

### Deployment Scripts
- `create-cross-account-roles.sh` - Deploy roles via CloudFormation StackSet
- `check-role-deployment.sh` - Verify role deployment across accounts
- `check-cross-account-access.sh` - Test role assumption and permissions

### Debug Scripts
- `debug-assume-role.sh` - Troubleshoot role assumption issues

## Role Details

### Role Name
`CrossAccountAuditRole`

### Trust Relationship
- **Principal**: Management account root
- **External ID**: `DrataCleanup2025`
- **Session Names**: `DrataRemediationSession`

### Permissions Summary

#### Base Permissions
- **ReadOnlyAccess** - AWS managed policy for general auditing

#### VPC Management
- VPC, subnet, route table, security group operations
- Internet gateway and NAT gateway management
- Network interface and VPC endpoint operations

#### NACL Security
- Network ACL rule creation, modification, and deletion
- Network ACL analysis and remediation

#### IAM Group-Based Access Control
- User analysis and policy inspection
- IAM group creation and management
- Policy migration from users to groups
- User-group membership management

#### Service Integration Analysis
- RDS, Lambda, ELB, ECS service inspection for VPC usage

## Deployment

### Prerequisites
- AWS CLI configured with Organization management account credentials
- CloudFormation StackSet permissions
- Target OUs and accounts identified

### Quick Start
```bash
# Deploy to all accounts in your organization
./create-cross-account-roles.sh

# Verify deployment
./check-role-deployment.sh

# Test access
./check-cross-account-access.sh
```

### Manual Deployment
```bash
# Deploy via StackSet
aws cloudformation create-stack-set \
  --stack-set-name CrossAccountAuditRoles \
  --template-body file://cross-account-role-template.yaml \
  --parameters ParameterKey=ManagementAccountId,ParameterValue=YOUR_MGMT_ACCOUNT_ID \
  --capabilities CAPABILITY_NAMED_IAM

# Deploy to target accounts
aws cloudformation create-stack-instances \
  --stack-set-name CrossAccountAuditRoles \
  --deployment-targets OrganizationalUnitIds=ou-3g6q-xym2ltt7 \
  --regions us-east-1
```

## Usage with Remediation Scripts

### VPC Cleanup
```bash
# Scripts in ../unused-vpcs/ use this role
cd ../unused-vpcs
./find-delete-default-vpcs.sh
```

### NACL Security Remediation
```bash
# Scripts in ../admin-port-nacls/ use this role
cd ../admin-port-nacls
./fix-admin-port-nacls.sh
```

### IAM Group-Based Access Control
```bash
# Scripts in ../non-group-privileges/ use this role
cd ../non-group-privileges
./analyze-iam-users.sh
./remediate-group-access.sh
./validate-group-access.sh
```

## Security Features

### Least Privilege Access
- Permissions scoped to specific remediation tasks
- No administrative access beyond required operations
- External ID requirement prevents unauthorized access

### Audit Trail
- All actions logged in CloudTrail
- Clear session naming for activity identification
- Role tags identify purpose and management

### Cross-Account Security
- Trust relationship limited to management account
- Temporary credentials with session tokens
- No permanent credential storage

## Monitoring and Compliance

### CloudTrail Integration
All role activities are logged with:
- **Role ARN**: Identifies the assumed role
- **Session Name**: Identifies the remediation activity
- **Source IP**: Shows where the assumption originated
- **API Calls**: Details all actions performed

### Compliance Evidence
- Role deployment provides evidence of security controls
- Activity logs support audit requirements
- Permission documentation demonstrates least privilege

## Troubleshooting

### Common Issues

#### Role Assumption Failures
```bash
# Check if role exists
aws iam get-role --role-name CrossAccountAuditRole --profile target-account

# Verify trust relationship
aws iam get-role --role-name CrossAccountAuditRole --query 'Role.AssumeRolePolicyDocument'

# Test assumption
./debug-assume-role.sh ACCOUNT_ID
```

#### Permission Denied Errors
```bash
# Check role permissions
aws iam list-attached-role-policies --role-name CrossAccountAuditRole
aws iam list-role-policies --role-name CrossAccountAuditRole

# Verify external ID
aws sts assume-role \
  --role-arn arn:aws:iam::ACCOUNT_ID:role/CrossAccountAuditRole \
  --role-session-name TestSession \
  --external-id DrataCleanup2025
```

#### StackSet Deployment Issues
```bash
# Check StackSet status
aws cloudformation describe-stack-set --stack-set-name CrossAccountAuditRoles

# Check stack instances
aws cloudformation list-stack-instances --stack-set-name CrossAccountAuditRoles

# Check failed deployments
aws cloudformation describe-stack-set-operation --stack-set-name CrossAccountAuditRoles --operation-id OPERATION_ID
```

## Updates and Maintenance

### Recent Changes
- **Role Name**: Changed from `CrossAccountVPCManagementRole` to `CrossAccountAuditRole`
- **IAM Permissions**: Added comprehensive IAM group-based access control permissions
- **Session Names**: Updated to reflect broader remediation scope

### Updating Existing Deployments
```bash
# Update StackSet with new template
aws cloudformation update-stack-set \
  --stack-set-name CrossAccountAuditRoles \
  --template-body file://cross-account-role-template.yaml \
  --parameters ParameterKey=ManagementAccountId,ParameterValue=YOUR_MGMT_ACCOUNT_ID \
  --capabilities CAPABILITY_NAMED_IAM

# Deploy updates to all instances
aws cloudformation create-stack-instances \
  --stack-set-name CrossAccountAuditRoles \
  --deployment-targets OrganizationalUnitIds=ou-3g6q-xym2ltt7 \
  --regions us-east-1 \
  --operation-preferences MaxConcurrentPercentage=100
```

## Support

### Documentation
- `IAM-PERMISSIONS-UPDATE.md` - Detailed permission changes
- Individual remediation README files in respective directories
- AWS CloudFormation StackSet documentation

### Validation Scripts
- `check-role-deployment.sh` - Verify role exists across accounts
- `check-cross-account-access.sh` - Test role functionality
- `debug-assume-role.sh` - Troubleshoot specific issues

### Best Practices
1. **Test in development accounts first** before organization-wide deployment
2. **Monitor CloudTrail logs** for unexpected activity
3. **Regularly validate role permissions** match current requirements
4. **Update external IDs periodically** for enhanced security
5. **Document any custom modifications** for audit purposes
